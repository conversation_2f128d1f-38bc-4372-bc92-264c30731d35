# 🚀 Deployment Guide - Parking Management System

This guide covers various deployment options for the Parking Management System in production environments.

## 🌐 Production Deployment Options

### 1. Traditional Server Deployment
### 2. Docker Deployment
### 3. Cloud Platform Deployment (AWS, Azure, GCP)
### 4. Container Orchestration (Kubernetes)

---

## 🖥️ Traditional Server Deployment

### Prerequisites
- Ubuntu 20.04+ or CentOS 8+
- Node.js 18+
- MySQL 8.0+
- Nginx (for reverse proxy)
- SSL Certificate (Let's Encrypt recommended)

### Step 1: Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install MySQL
sudo apt install mysql-server -y
sudo mysql_secure_installation

# Install Nginx
sudo apt install nginx -y

# Install PM2 for process management
sudo npm install -g pm2
```

### Step 2: Database Setup

```bash
# Create database and user
sudo mysql -u root -p

CREATE DATABASE parking_management;
CREATE USER 'parking_user'@'localhost' IDENTIFIED BY 'STRONG_PASSWORD_HERE';
GRANT ALL PRIVILEGES ON parking_management.* TO 'parking_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# Import schema
mysql -u parking_user -p parking_management < database/schema.sql
mysql -u parking_user -p parking_management < database/sample-data.sql
```

### Step 3: Application Deployment

```bash
# Clone repository
git clone <repository-url> /var/www/parking-management
cd /var/www/parking-management

# Backend setup
cd backend
npm ci --production
npm run build

# Create production environment file
cp .env.example .env.production
# Edit .env.production with production values

# Frontend setup
cd ../frontend
npm ci
ng build --configuration production

# Set proper permissions
sudo chown -R www-data:www-data /var/www/parking-management
sudo chmod -R 755 /var/www/parking-management
```

### Step 4: PM2 Configuration

Create `ecosystem.config.js`:
```javascript
module.exports = {
  apps: [{
    name: 'parking-backend',
    script: 'dist/main.js',
    cwd: '/var/www/parking-management/backend',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    instances: 'max',
    exec_mode: 'cluster',
    max_memory_restart: '1G',
    error_file: '/var/log/parking/backend-error.log',
    out_file: '/var/log/parking/backend-out.log',
    log_file: '/var/log/parking/backend.log'
  }]
};
```

Start the application:
```bash
# Create log directory
sudo mkdir -p /var/log/parking
sudo chown www-data:www-data /var/log/parking

# Start with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Step 5: Nginx Configuration

Create `/etc/nginx/sites-available/parking-management`:
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # Frontend
    location / {
        root /var/www/parking-management/frontend/dist/frontend;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:3000/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static assets caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/parking-management /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

---

## 🐳 Docker Deployment

### Production Docker Compose

Create `docker-compose.prod.yml`:
```yaml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: parking_mysql_prod
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: parking_management
      MYSQL_USER: parking_user
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./database/sample-data.sql:/docker-entrypoint-initdb.d/02-sample-data.sql
    networks:
      - parking_network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: parking_backend_prod
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USERNAME: parking_user
      DB_PASSWORD: ${MYSQL_PASSWORD}
      DB_DATABASE: parking_management
      JWT_SECRET: ${JWT_SECRET}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
    depends_on:
      - mysql
    networks:
      - parking_network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: parking_frontend_prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - parking_network

volumes:
  mysql_data:

networks:
  parking_network:
    driver: bridge
```

### Environment Variables

Create `.env.prod`:
```env
MYSQL_ROOT_PASSWORD=super_secure_root_password
MYSQL_PASSWORD=secure_parking_password
JWT_SECRET=your-super-secure-jwt-secret-for-production
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-for-production
```

### Deploy with Docker
```bash
# Build and start
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Scale backend
docker-compose -f docker-compose.prod.yml up -d --scale backend=3
```

---

## ☁️ Cloud Platform Deployment

### AWS Deployment

#### Using AWS ECS (Elastic Container Service)

1. **Create ECR Repositories**
```bash
aws ecr create-repository --repository-name parking-backend
aws ecr create-repository --repository-name parking-frontend
```

2. **Build and Push Images**
```bash
# Get login token
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.us-east-1.amazonaws.com

# Build and push backend
docker build -t parking-backend ./backend
docker tag parking-backend:latest <account-id>.dkr.ecr.us-east-1.amazonaws.com/parking-backend:latest
docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/parking-backend:latest

# Build and push frontend
docker build -t parking-frontend ./frontend
docker tag parking-frontend:latest <account-id>.dkr.ecr.us-east-1.amazonaws.com/parking-frontend:latest
docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/parking-frontend:latest
```

3. **Create ECS Task Definition**
4. **Set up Application Load Balancer**
5. **Configure RDS for MySQL**
6. **Deploy ECS Service**

### Azure Deployment

#### Using Azure Container Instances

```bash
# Create resource group
az group create --name parking-management --location eastus

# Create MySQL database
az mysql server create --resource-group parking-management --name parking-mysql --admin-user parking_admin --admin-password SecurePassword123! --sku-name B_Gen5_1

# Deploy containers
az container create --resource-group parking-management --name parking-backend --image your-registry/parking-backend:latest --environment-variables DB_HOST=parking-mysql.mysql.database.azure.com

az container create --resource-group parking-management --name parking-frontend --image your-registry/parking-frontend:latest --ports 80 443
```

---

## 🔧 Production Configuration

### Environment Variables

**Backend (.env.production):**
```env
# Database
DB_HOST=your-mysql-host
DB_PORT=3306
DB_USERNAME=parking_user
DB_PASSWORD=your-secure-password
DB_DATABASE=parking_management

# JWT (Use strong, unique secrets)
JWT_SECRET=your-256-bit-secret-key-here
JWT_EXPIRES_IN=15m
JWT_REFRESH_SECRET=your-256-bit-refresh-secret-here
JWT_REFRESH_EXPIRES_IN=7d

# Application
PORT=3000
NODE_ENV=production
FRONTEND_URL=https://your-domain.com

# CORS
CORS_ORIGIN=https://your-domain.com
CORS_CREDENTIALS=true

# Logging
LOG_LEVEL=error
```

**Frontend (environment.prod.ts):**
```typescript
export const environment = {
  production: true,
  apiUrl: 'https://your-domain.com/api'
};
```

### Security Checklist

- [ ] Use HTTPS everywhere
- [ ] Strong JWT secrets (256-bit minimum)
- [ ] Secure database passwords
- [ ] Enable firewall (only ports 80, 443, 22)
- [ ] Regular security updates
- [ ] Database backups
- [ ] Rate limiting configured
- [ ] CORS properly configured
- [ ] Security headers enabled
- [ ] Log monitoring setup

### Performance Optimization

- [ ] Enable Gzip compression
- [ ] Configure CDN for static assets
- [ ] Database indexing optimized
- [ ] Connection pooling enabled
- [ ] Caching strategy implemented
- [ ] Load balancing configured
- [ ] Health checks enabled
- [ ] Monitoring and alerting setup

### Backup Strategy

```bash
# Database backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u parking_user -p parking_management > /backups/parking_db_$DATE.sql
gzip /backups/parking_db_$DATE.sql

# Keep only last 30 days
find /backups -name "parking_db_*.sql.gz" -mtime +30 -delete
```

### Monitoring

**Health Check Endpoints:**
- Backend: `GET /health`
- Database: Connection monitoring
- Frontend: Availability monitoring

**Recommended Monitoring Tools:**
- **Application**: New Relic, DataDog
- **Infrastructure**: Prometheus + Grafana
- **Logs**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **Uptime**: Pingdom, UptimeRobot

---

## 🔄 CI/CD Pipeline

### GitHub Actions Example

Create `.github/workflows/deploy.yml`:
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: |
          cd backend && npm ci
          cd ../frontend && npm ci
          
      - name: Run tests
        run: |
          cd backend && npm test
          cd ../frontend && npm test
          
      - name: Build applications
        run: |
          cd backend && npm run build
          cd ../frontend && ng build --prod
          
      - name: Deploy to server
        uses: appleboy/ssh-action@v0.1.4
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /var/www/parking-management
            git pull origin main
            cd backend && npm ci --production && npm run build
            cd ../frontend && npm ci && ng build --prod
            pm2 restart parking-backend
            sudo systemctl reload nginx
```

---

## 📞 Production Support

### Troubleshooting

**Common Production Issues:**

1. **High Memory Usage**
   - Monitor with `pm2 monit`
   - Adjust `max_memory_restart` in PM2 config

2. **Database Connection Issues**
   - Check connection pool settings
   - Monitor active connections
   - Verify firewall rules

3. **SSL Certificate Renewal**
   - Set up automatic renewal with certbot
   - Monitor certificate expiration

### Maintenance

**Regular Tasks:**
- Weekly security updates
- Monthly database optimization
- Quarterly dependency updates
- Annual security audit

**Emergency Procedures:**
- Database restore process
- Application rollback procedure
- Incident response plan
- Contact information for critical issues

---

**Deployment Version**: 1.0.0  
**Last Updated**: January 2024
