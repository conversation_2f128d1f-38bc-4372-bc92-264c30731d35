import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { forkJoin } from 'rxjs';
import { finalize } from 'rxjs/operators';

import { UserService, UserStats } from '../services/user.service';
import { PlotsService, Plot } from '../services/plots.service';
import { VehiclesService, Vehicle } from '../services/vehicles.service';
import { GuestsService, Guest } from '../services/guests.service';
import { NotificationService } from '../../core/services/notification.service';
import { AuthService } from '../../core/services/auth.service';
import { User } from '../../core/models/user.model';

@Component({
  selector: 'app-user-dashboard',
  templateUrl: './user-dashboard.component.html',
  styleUrls: ['./user-dashboard.component.scss']
})
export class UserDashboardComponent implements OnInit {
  currentUser: User | null = null;
  stats: UserStats | null = null;
  recentPlots: Plot[] = [];
  recentVehicles: Vehicle[] = [];
  expectedGuests: Guest[] = [];
  overdueGuests: Guest[] = [];
  isLoading = true;

  constructor(
    private userService: UserService,
    private plotsService: PlotsService,
    private vehiclesService: VehiclesService,
    private guestsService: GuestsService,
    private authService: AuthService,
    private notificationService: NotificationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.currentUser = this.authService.getCurrentUser();
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    this.isLoading = true;

    forkJoin({
      stats: this.userService.getUserStats(),
      plots: this.plotsService.getMyPlots(),
      vehicles: this.vehiclesService.getMyVehicles(),
      expectedGuests: this.guestsService.getExpectedGuests(),
      overdueGuests: this.guestsService.getOverdueGuests()
    }).pipe(
      finalize(() => this.isLoading = false)
    ).subscribe({
      next: (data) => {
        this.stats = data.stats;
        this.recentPlots = data.plots.slice(0, 3); // Show only 3 recent plots
        this.recentVehicles = data.vehicles.slice(0, 3); // Show only 3 recent vehicles
        this.expectedGuests = data.expectedGuests.slice(0, 5); // Show only 5 expected guests
        this.overdueGuests = data.overdueGuests;
      },
      error: (error) => {
        console.error('Error loading dashboard data:', error);
        this.notificationService.showError('Failed to load dashboard data');
      }
    });
  }

  refreshDashboard(): void {
    this.loadDashboardData();
  }

  navigateToPlots(): void {
    this.router.navigate(['/user/plots']);
  }

  navigateToVehicles(): void {
    this.router.navigate(['/user/vehicles']);
  }

  navigateToGuests(): void {
    this.router.navigate(['/user/guests']);
  }

  navigateToPlotDetail(plotId: number): void {
    this.router.navigate(['/user/plots', plotId]);
  }

  getOccupancyColor(): string {
    if (!this.stats) return 'text-gray-600';
    
    const rate = this.stats.occupancyRate;
    if (rate >= 90) return 'text-red-600';
    if (rate >= 70) return 'text-yellow-600';
    return 'text-green-600';
  }

  getOccupancyIcon(): string {
    if (!this.stats) return 'info';
    
    const rate = this.stats.occupancyRate;
    if (rate >= 90) return 'warning';
    if (rate >= 70) return 'schedule';
    return 'check_circle';
  }

  getVehicleStatusIcon(vehicle: Vehicle): string {
    return vehicle.isCurrentlyParked ? 'local_parking' : 'directions_car';
  }

  getVehicleStatusColor(vehicle: Vehicle): string {
    return vehicle.isCurrentlyParked ? 'text-green-600' : 'text-blue-600';
  }

  getVehicleStatusText(vehicle: Vehicle): string {
    return vehicle.isCurrentlyParked ? 'Parked' : 'Available';
  }

  getGuestStatusColor(guest: Guest): string {
    if (guest.isOverdue) return 'text-red-600';
    if (guest.hasArrived) return 'text-green-600';
    return 'text-blue-600';
  }

  getGuestStatusIcon(guest: Guest): string {
    if (guest.isOverdue) return 'schedule';
    if (guest.hasArrived) return 'check_circle';
    return 'schedule';
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  formatTime(dateString: string): string {
    return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  getWelcomeMessage(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  }
}
