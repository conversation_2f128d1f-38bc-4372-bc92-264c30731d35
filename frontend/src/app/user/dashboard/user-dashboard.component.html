<div class="p-6 max-w-7xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">
          {{ getWelcomeMessage() }}, {{ currentUser?.firstName }}!
        </h1>
        <p class="mt-2 text-gray-600">Here's what's happening with your parking management</p>
      </div>
      <button
        mat-raised-button
        color="primary"
        (click)="refreshDashboard()"
        [disabled]="isLoading"
        class="flex items-center space-x-2"
      >
        <mat-icon>refresh</mat-icon>
        <span>Refresh</span>
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex justify-center py-12">
    <app-loading-spinner size="large" message="Loading dashboard..."></app-loading-spinner>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!isLoading" class="space-y-8">
    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Plots -->
      <mat-card class="p-6 cursor-pointer hover:shadow-lg transition-shadow" (click)="navigateToPlots()">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <mat-icon class="text-blue-600">location_city</mat-icon>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">My Plots</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats?.totalPlots || 0 }}</p>
          </div>
        </div>
      </mat-card>

      <!-- Total Slots -->
      <mat-card class="p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <mat-icon class="text-green-600">local_parking</mat-icon>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Slots</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats?.totalSlots || 0 }}</p>
          </div>
        </div>
      </mat-card>

      <!-- Vehicles -->
      <mat-card class="p-6 cursor-pointer hover:shadow-lg transition-shadow" (click)="navigateToVehicles()">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <mat-icon class="text-purple-600">directions_car</mat-icon>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">My Vehicles</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats?.totalVehicles || 0 }}</p>
          </div>
        </div>
      </mat-card>

      <!-- Occupancy Rate -->
      <mat-card class="p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <mat-icon [class]="getOccupancyColor()">{{ getOccupancyIcon() }}</mat-icon>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Occupancy</p>
            <p class="text-2xl font-bold" [class]="getOccupancyColor()">
              {{ stats?.occupancyRate || 0 | number:'1.1-1' }}%
            </p>
          </div>
        </div>
      </mat-card>
    </div>

    <!-- Quick Actions -->
    <mat-card class="p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button
          mat-raised-button
          color="primary"
          (click)="navigateToPlots()"
          class="h-16 flex items-center justify-center space-x-2"
        >
          <mat-icon>add_location</mat-icon>
          <span>Manage Plots</span>
        </button>
        
        <button
          mat-raised-button
          color="accent"
          (click)="navigateToVehicles()"
          class="h-16 flex items-center justify-center space-x-2"
        >
          <mat-icon>add</mat-icon>
          <span>Add Vehicle</span>
        </button>
        
        <button
          mat-raised-button
          (click)="navigateToGuests()"
          class="h-16 flex items-center justify-center space-x-2"
        >
          <mat-icon>person_add</mat-icon>
          <span>Manage Guests</span>
        </button>
      </div>
    </mat-card>

    <!-- Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Recent Plots -->
      <mat-card class="p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Recent Plots</h3>
          <button mat-button color="primary" (click)="navigateToPlots()">
            View All
          </button>
        </div>
        
        <div *ngIf="recentPlots.length > 0; else noPlots" class="space-y-3">
          <div
            *ngFor="let plot of recentPlots"
            class="p-4 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors"
            (click)="navigateToPlotDetail(plot.id)"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <h4 class="font-medium text-gray-900">{{ plot.name }}</h4>
                <p class="text-sm text-gray-600">{{ plot.location }}</p>
                <div class="flex items-center space-x-4 mt-2">
                  <span class="text-xs text-gray-500">{{ plot.type }}</span>
                  <span class="text-xs text-gray-500">
                    {{ plot.availableSlots || 0 }}/{{ plot.totalSlots }} available
                  </span>
                </div>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium text-gray-900">
                  {{ plot.occupancyRate || 0 | number:'1.0-0' }}%
                </div>
                <div class="text-xs text-gray-500">Occupied</div>
              </div>
            </div>
          </div>
        </div>
        
        <ng-template #noPlots>
          <div class="text-center py-8">
            <mat-icon class="text-gray-400 text-4xl mb-2">location_city</mat-icon>
            <p class="text-gray-500 mb-4">No plots yet</p>
            <button mat-raised-button color="primary" (click)="navigateToPlots()">
              Create Your First Plot
            </button>
          </div>
        </ng-template>
      </mat-card>

      <!-- Recent Vehicles -->
      <mat-card class="p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Recent Vehicles</h3>
          <button mat-button color="primary" (click)="navigateToVehicles()">
            View All
          </button>
        </div>
        
        <div *ngIf="recentVehicles.length > 0; else noVehicles" class="space-y-3">
          <div
            *ngFor="let vehicle of recentVehicles"
            class="p-4 bg-gray-50 rounded-lg"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <mat-icon [class]="getVehicleStatusColor(vehicle)">
                    {{ getVehicleStatusIcon(vehicle) }}
                  </mat-icon>
                </div>
                <div>
                  <h4 class="font-medium text-gray-900">{{ vehicle.vehicleNumber }}</h4>
                  <p class="text-sm text-gray-600">{{ vehicle.ownerName }}</p>
                  <p class="text-xs text-gray-500">{{ vehicle.type }}</p>
                </div>
              </div>
              <div class="text-right">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  [class]="vehicle.isCurrentlyParked ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'"
                >
                  {{ getVehicleStatusText(vehicle) }}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <ng-template #noVehicles>
          <div class="text-center py-8">
            <mat-icon class="text-gray-400 text-4xl mb-2">directions_car</mat-icon>
            <p class="text-gray-500 mb-4">No vehicles registered</p>
            <button mat-raised-button color="primary" (click)="navigateToVehicles()">
              Add Your First Vehicle
            </button>
          </div>
        </ng-template>
      </mat-card>
    </div>

    <!-- Expected Guests -->
    <mat-card class="p-6" *ngIf="expectedGuests.length > 0 || overdueGuests.length > 0">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">
          Expected Guests
          <span *ngIf="overdueGuests.length > 0" class="ml-2 text-red-600">({{ overdueGuests.length }} overdue)</span>
        </h3>
        <button mat-button color="primary" (click)="navigateToGuests()">
          View All
        </button>
      </div>
      
      <div class="space-y-3">
        <!-- Overdue Guests -->
        <div
          *ngFor="let guest of overdueGuests"
          class="p-4 bg-red-50 border border-red-200 rounded-lg"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <mat-icon class="text-red-600">schedule</mat-icon>
              <div>
                <h4 class="font-medium text-gray-900">{{ guest.name }}</h4>
                <p class="text-sm text-gray-600">{{ guest.vehicleNumber }}</p>
                <p class="text-xs text-red-600">Overdue since {{ formatTime(guest.expectedVisitTime) }}</p>
              </div>
            </div>
            <span class="text-xs text-red-600 font-medium">OVERDUE</span>
          </div>
        </div>
        
        <!-- Expected Guests -->
        <div
          *ngFor="let guest of expectedGuests"
          class="p-4 bg-blue-50 border border-blue-200 rounded-lg"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <mat-icon [class]="getGuestStatusColor(guest)">
                {{ getGuestStatusIcon(guest) }}
              </mat-icon>
              <div>
                <h4 class="font-medium text-gray-900">{{ guest.name }}</h4>
                <p class="text-sm text-gray-600">{{ guest.vehicleNumber }}</p>
                <p class="text-xs text-gray-500">Expected at {{ formatTime(guest.expectedVisitTime) }}</p>
              </div>
            </div>
            <span class="text-xs text-blue-600 font-medium">{{ guest.status }}</span>
          </div>
        </div>
      </div>
    </mat-card>
  </div>
</div>
