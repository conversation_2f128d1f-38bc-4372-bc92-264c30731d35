:host {
  display: block;
  min-height: 100vh;
  background-color: #f9fafb;
}

.mat-mdc-card {
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.2s ease-in-out;
}

.mat-mdc-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.plot-card,
.vehicle-card {
  transition: background-color 0.2s ease-in-out;
}

.plot-card:hover,
.vehicle-card:hover {
  background-color: #f3f4f6;
}

.quick-action-btn {
  height: 64px;
  font-size: 16px;
}

// Responsive adjustments
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }
  
  .p-6 {
    padding: 1rem;
  }
  
  .quick-action-btn {
    height: 48px;
    font-size: 14px;
  }
}
