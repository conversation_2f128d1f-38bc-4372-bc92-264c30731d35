import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';

import { VehiclesService, Vehicle } from '../services/vehicles.service';
import { CreateVehicleDialogComponent } from './create-vehicle-dialog/create-vehicle-dialog.component';

@Component({
  selector: 'app-vehicles-management',
  templateUrl: './vehicles-management.component.html',
  styleUrls: ['./vehicles-management.component.scss']
})
export class VehiclesManagementComponent implements OnInit {
  vehicles: Vehicle[] = [];
  isLoading = false;

  constructor(
    private vehiclesService: VehiclesService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadVehicles();
  }

  loadVehicles(): void {
    this.isLoading = true;
    this.vehiclesService.getMyVehicles().subscribe({
      next: (vehicles) => {
        this.vehicles = vehicles;
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
      }
    });
  }

  openCreateDialog(): void {
    const dialogRef = this.dialog.open(CreateVehicleDialogComponent, {
      width: '500px'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadVehicles();
      }
    });
  }
}
