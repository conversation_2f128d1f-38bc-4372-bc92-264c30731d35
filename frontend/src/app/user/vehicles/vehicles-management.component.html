<div class="p-6 max-w-7xl mx-auto">
  <div class="flex items-center justify-between mb-8">
    <div>
      <h1 class="text-3xl font-bold text-gray-900">My Vehicles</h1>
      <p class="mt-2 text-gray-600">Manage your registered vehicles</p>
    </div>
    <button mat-raised-button color="primary" (click)="openCreateDialog()">
      <mat-icon>add</mat-icon>
      Add Vehicle
    </button>
  </div>

  <div *ngIf="isLoading" class="flex justify-center py-12">
    <app-loading-spinner size="large" message="Loading vehicles..."></app-loading-spinner>
  </div>

  <div *ngIf="!isLoading && vehicles.length === 0" class="text-center py-12">
    <mat-card class="p-8">
      <mat-icon class="text-gray-400 text-6xl mb-4">directions_car</mat-icon>
      <h3 class="text-xl font-medium text-gray-900 mb-2">No vehicles yet</h3>
      <p class="text-gray-600 mb-6">Add your first vehicle to start managing parking assignments.</p>
      <button mat-raised-button color="primary" (click)="openCreateDialog()">
        Add Your First Vehicle
      </button>
    </mat-card>
  </div>

  <div *ngIf="!isLoading && vehicles.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <mat-card *ngFor="let vehicle of vehicles" class="p-6">
      <h3 class="text-lg font-semibold text-gray-900">{{ vehicle.vehicleNumber }}</h3>
      <p class="text-gray-600">{{ vehicle.ownerName }}</p>
      <p class="text-sm text-gray-500">{{ vehicle.type }}</p>
    </mat-card>
  </div>
</div>
