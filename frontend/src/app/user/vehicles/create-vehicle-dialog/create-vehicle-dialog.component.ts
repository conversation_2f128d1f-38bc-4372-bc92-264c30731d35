import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';

import { VehiclesService } from '../../services/vehicles.service';

@Component({
  selector: 'app-create-vehicle-dialog',
  templateUrl: './create-vehicle-dialog.component.html',
  styleUrls: ['./create-vehicle-dialog.component.scss']
})
export class CreateVehicleDialogComponent {
  vehicleForm: FormGroup;
  isLoading = false;

  vehicleTypes = [
    { value: 'Car', label: 'Car' },
    { value: 'Motorcycle', label: 'Motorcycle' },
    { value: 'Truck', label: 'Truck' },
    { value: 'Van', label: 'Van' }
  ];

  constructor(
    private formBuilder: FormBuilder,
    private vehiclesService: VehiclesService,
    public dialogRef: MatDialogRef<CreateVehicleDialogComponent>
  ) {
    this.vehicleForm = this.formBuilder.group({
      vehicleNumber: ['', [Validators.required]],
      ownerName: ['', [Validators.required]],
      type: ['', [Validators.required]],
      contact: ['', [Validators.required]]
    });
  }

  onSubmit(): void {
    if (this.vehicleForm.valid && !this.isLoading) {
      this.isLoading = true;
      
      this.vehiclesService.createVehicle(this.vehicleForm.value).subscribe({
        next: (vehicle) => {
          this.dialogRef.close(vehicle);
        },
        error: () => {
          this.isLoading = false;
        }
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
