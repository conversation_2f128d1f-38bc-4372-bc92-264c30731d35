<div class="p-6">
  <h2 class="text-xl font-semibold text-gray-900 mb-6">Add New Vehicle</h2>
  
  <form [formGroup]="vehicleForm" (ngSubmit)="onSubmit()" class="space-y-4">
    <mat-form-field appearance="outline" class="w-full">
      <mat-label>Vehicle Number</mat-label>
      <input matInput formControlName="vehicleNumber" placeholder="e.g., ABC-1234">
    </mat-form-field>

    <mat-form-field appearance="outline" class="w-full">
      <mat-label>Owner Name</mat-label>
      <input matInput formControlName="ownerName">
    </mat-form-field>

    <mat-form-field appearance="outline" class="w-full">
      <mat-label>Vehicle Type</mat-label>
      <mat-select formControlName="type">
        <mat-option *ngFor="let type of vehicleTypes" [value]="type.value">
          {{ type.label }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="outline" class="w-full">
      <mat-label>Contact</mat-label>
      <input matInput formControlName="contact" placeholder="Phone number">
    </mat-form-field>

    <div class="flex justify-end space-x-3 pt-4">
      <button type="button" mat-button (click)="onCancel()">Cancel</button>
      <button type="submit" mat-raised-button color="primary" [disabled]="isLoading">
        Add Vehicle
      </button>
    </div>
  </form>
</div>
