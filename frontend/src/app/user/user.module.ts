import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SharedModule } from '../shared/shared.module';
import { UserRoutingModule } from './user-routing.module';
import { UserDashboardComponent } from './dashboard/user-dashboard.component';
import { PlotsManagementComponent } from './plots/plots-management.component';
import { PlotDetailComponent } from './plots/plot-detail/plot-detail.component';
import { CreatePlotDialogComponent } from './plots/create-plot-dialog/create-plot-dialog.component';
import { EditPlotDialogComponent } from './plots/edit-plot-dialog/edit-plot-dialog.component';
import { VehiclesManagementComponent } from './vehicles/vehicles-management.component';
import { CreateVehicleDialogComponent } from './vehicles/create-vehicle-dialog/create-vehicle-dialog.component';
import { EditVehicleDialogComponent } from './vehicles/edit-vehicle-dialog/edit-vehicle-dialog.component';
import { GuestsManagementComponent } from './guests/guests-management.component';
import { CreateGuestDialogComponent } from './guests/create-guest-dialog/create-guest-dialog.component';
import { EditGuestDialogComponent } from './guests/edit-guest-dialog/edit-guest-dialog.component';
import { SlotAssignmentDialogComponent } from './plots/slot-assignment-dialog/slot-assignment-dialog.component';
import { ParkingLogsComponent } from './parking-logs/parking-logs.component';
import { UserProfileComponent } from './profile/user-profile.component';

@NgModule({
  declarations: [
    UserDashboardComponent,
    PlotsManagementComponent,
    PlotDetailComponent,
    CreatePlotDialogComponent,
    EditPlotDialogComponent,
    VehiclesManagementComponent,
    CreateVehicleDialogComponent,
    EditVehicleDialogComponent,
    GuestsManagementComponent,
    CreateGuestDialogComponent,
    EditGuestDialogComponent,
    SlotAssignmentDialogComponent,
    ParkingLogsComponent,
    UserProfileComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    UserRoutingModule
  ]
})
export class UserModule { }
