import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { MainLayoutComponent } from '../shared/layouts/main-layout/main-layout.component';
import { UserDashboardComponent } from './dashboard/user-dashboard.component';
import { PlotsManagementComponent } from './plots/plots-management.component';
import { PlotDetailComponent } from './plots/plot-detail/plot-detail.component';
import { VehiclesManagementComponent } from './vehicles/vehicles-management.component';
import { GuestsManagementComponent } from './guests/guests-management.component';
import { ParkingLogsComponent } from './parking-logs/parking-logs.component';
import { UserProfileComponent } from './profile/user-profile.component';

const routes: Routes = [
  {
    path: '',
    component: MainLayoutComponent,
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        component: UserDashboardComponent
      },
      {
        path: 'plots',
        component: PlotsManagementComponent
      },
      {
        path: 'plots/:id',
        component: PlotDetailComponent
      },
      {
        path: 'vehicles',
        component: VehiclesManagementComponent
      },
      {
        path: 'guests',
        component: GuestsManagementComponent
      },
      {
        path: 'parking-logs',
        component: ParkingLogsComponent
      },
      {
        path: 'profile',
        component: UserProfileComponent
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UserRoutingModule { }
