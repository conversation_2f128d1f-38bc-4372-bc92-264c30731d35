import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

import { environment } from '../../../environments/environment';
import { PaginatedResponse } from '../../core/models/api-response.model';

export interface Vehicle {
  id: number;
  vehicleNumber: string;
  ownerName: string;
  type: 'Car' | 'Motorcycle' | 'Truck' | 'Van';
  contact: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  parkingSlots?: any[];
  currentParkingSlot?: any;
  isCurrentlyParked?: boolean;
}

export interface CreateVehicleRequest {
  vehicleNumber: string;
  ownerName: string;
  type: string;
  contact: string;
}

export interface UpdateVehicleRequest {
  ownerName?: string;
  type?: string;
  contact?: string;
  isActive?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class VehiclesService {
  private readonly API_URL = `${environment.apiUrl}/api/vehicles`;

  constructor(private http: HttpClient) {}

  getVehicles(page: number = 1, limit: number = 10, search?: string): Observable<PaginatedResponse<Vehicle>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    return this.http.get<PaginatedResponse<Vehicle>>(this.API_URL, { params });
  }

  getMyVehicles(): Observable<Vehicle[]> {
    return this.http.get<Vehicle[]>(`${this.API_URL}/my-vehicles`);
  }

  getAvailableVehicles(): Observable<Vehicle[]> {
    return this.http.get<Vehicle[]>(`${this.API_URL}/available`);
  }

  getVehicle(id: number): Observable<Vehicle> {
    return this.http.get<Vehicle>(`${this.API_URL}/${id}`);
  }

  createVehicle(vehicleData: CreateVehicleRequest): Observable<Vehicle> {
    return this.http.post<Vehicle>(this.API_URL, vehicleData);
  }

  updateVehicle(id: number, vehicleData: UpdateVehicleRequest): Observable<Vehicle> {
    return this.http.patch<Vehicle>(`${this.API_URL}/${id}`, vehicleData);
  }

  deleteVehicle(id: number): Observable<{ message: string }> {
    return this.http.delete<{ message: string }>(`${this.API_URL}/${id}`);
  }
}
