import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

import { environment } from '../../../environments/environment';
import { PaginatedResponse } from '../../core/models/api-response.model';

export interface Guest {
  id: number;
  name: string;
  contact: string;
  vehicleNumber: string;
  purpose: string;
  expectedVisitTime: string;
  actualArrivalTime?: string;
  actualDepartureTime?: string;
  status: 'Expected' | 'Arrived' | 'Departed';
  createdAt: string;
  updatedAt: string;
  parkingLogs?: any[];
  isExpected?: boolean;
  hasArrived?: boolean;
  hasDeparted?: boolean;
  isOverdue?: boolean;
}

export interface CreateGuestRequest {
  name: string;
  contact: string;
  vehicleNumber: string;
  purpose: string;
  expectedVisitTime: string;
}

export interface UpdateGuestRequest {
  name?: string;
  contact?: string;
  vehicleNumber?: string;
  purpose?: string;
  expectedVisitTime?: string;
  actualArrivalTime?: string;
  actualDepartureTime?: string;
  status?: string;
}

@Injectable({
  providedIn: 'root'
})
export class GuestsService {
  private readonly API_URL = `${environment.apiUrl}/api/guests`;

  constructor(private http: HttpClient) {}

  getGuests(page: number = 1, limit: number = 10, search?: string): Observable<PaginatedResponse<Guest>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    return this.http.get<PaginatedResponse<Guest>>(this.API_URL, { params });
  }

  getMyGuests(): Observable<Guest[]> {
    return this.http.get<Guest[]>(`${this.API_URL}/my-guests`);
  }

  getExpectedGuests(): Observable<Guest[]> {
    return this.http.get<Guest[]>(`${this.API_URL}/expected`);
  }

  getOverdueGuests(): Observable<Guest[]> {
    return this.http.get<Guest[]>(`${this.API_URL}/overdue`);
  }

  getGuest(id: number): Observable<Guest> {
    return this.http.get<Guest>(`${this.API_URL}/${id}`);
  }

  createGuest(guestData: CreateGuestRequest): Observable<Guest> {
    return this.http.post<Guest>(this.API_URL, guestData);
  }

  updateGuest(id: number, guestData: UpdateGuestRequest): Observable<Guest> {
    return this.http.patch<Guest>(`${this.API_URL}/${id}`, guestData);
  }

  deleteGuest(id: number): Observable<{ message: string }> {
    return this.http.delete<{ message: string }>(`${this.API_URL}/${id}`);
  }

  markArrived(id: number): Observable<Guest> {
    return this.http.post<Guest>(`${this.API_URL}/${id}/arrived`, {});
  }

  markDeparted(id: number): Observable<Guest> {
    return this.http.post<Guest>(`${this.API_URL}/${id}/departed`, {});
  }
}
