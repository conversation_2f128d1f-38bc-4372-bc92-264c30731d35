import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

import { environment } from '../../../environments/environment';
import { PaginatedResponse } from '../../core/models/api-response.model';

export interface Plot {
  id: number;
  name: string;
  type: 'Mall' | 'Apartment' | 'Office' | 'Residential';
  location: string;
  totalSlots: number;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  parkingSlots?: ParkingSlot[];
  availableSlots?: number;
  occupiedSlots?: number;
  occupancyRate?: number;
}

export interface ParkingSlot {
  id: number;
  slotNumber: string;
  status: 'Available' | 'Occupied' | 'Maintenance';
  vehicleId?: number;
  allocatedAt?: string;
  vehicle?: any;
}

export interface CreatePlotRequest {
  name: string;
  type: string;
  location: string;
  totalSlots: number;
  description?: string;
}

export interface UpdatePlotRequest {
  name?: string;
  type?: string;
  location?: string;
  description?: string;
  isActive?: boolean;
}

export interface AssignSlotRequest {
  vehicleId?: number;
  guestId?: number;
  notes?: string;
}

@Injectable({
  providedIn: 'root'
})
export class PlotsService {
  private readonly API_URL = `${environment.apiUrl}/api/plots`;
  private readonly SLOTS_API_URL = `${environment.apiUrl}/api/parking-slots`;

  constructor(private http: HttpClient) {}

  getPlots(page: number = 1, limit: number = 10, search?: string): Observable<PaginatedResponse<Plot>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    return this.http.get<PaginatedResponse<Plot>>(this.API_URL, { params });
  }

  getMyPlots(): Observable<Plot[]> {
    return this.http.get<Plot[]>(`${this.API_URL}/my-plots`);
  }

  getPlot(id: number): Observable<Plot> {
    return this.http.get<Plot>(`${this.API_URL}/${id}`);
  }

  createPlot(plotData: CreatePlotRequest): Observable<Plot> {
    return this.http.post<Plot>(this.API_URL, plotData);
  }

  updatePlot(id: number, plotData: UpdatePlotRequest): Observable<Plot> {
    return this.http.patch<Plot>(`${this.API_URL}/${id}`, plotData);
  }

  deletePlot(id: number): Observable<{ message: string }> {
    return this.http.delete<{ message: string }>(`${this.API_URL}/${id}`);
  }

  getPlotStats(id: number): Observable<any> {
    return this.http.get<any>(`${this.API_URL}/${id}/stats`);
  }

  // Parking Slots
  getSlots(plotId?: number): Observable<ParkingSlot[]> {
    let params = new HttpParams();
    if (plotId) {
      params = params.set('plotId', plotId.toString());
    }
    return this.http.get<ParkingSlot[]>(this.SLOTS_API_URL, { params });
  }

  getAvailableSlots(plotId: number): Observable<ParkingSlot[]> {
    return this.http.get<ParkingSlot[]>(`${this.SLOTS_API_URL}/available?plotId=${plotId}`);
  }

  assignSlot(slotId: number, assignData: AssignSlotRequest): Observable<ParkingSlot> {
    return this.http.post<ParkingSlot>(`${this.SLOTS_API_URL}/${slotId}/assign`, assignData);
  }

  releaseSlot(slotId: number, notes?: string): Observable<ParkingSlot> {
    const data = notes ? { notes } : {};
    return this.http.post<ParkingSlot>(`${this.SLOTS_API_URL}/${slotId}/release`, data);
  }

  updateSlot(slotId: number, slotData: any): Observable<ParkingSlot> {
    return this.http.patch<ParkingSlot>(`${this.SLOTS_API_URL}/${slotId}`, slotData);
  }
}
