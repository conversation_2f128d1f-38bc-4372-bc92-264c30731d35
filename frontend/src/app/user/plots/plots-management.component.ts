import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { finalize } from 'rxjs/operators';

import { PlotsService, Plot } from '../services/plots.service';
import { NotificationService } from '../../core/services/notification.service';
import { CreatePlotDialogComponent } from './create-plot-dialog/create-plot-dialog.component';
import { EditPlotDialogComponent } from './edit-plot-dialog/edit-plot-dialog.component';
import { ConfirmDialogComponent } from '../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-plots-management',
  templateUrl: './plots-management.component.html',
  styleUrls: ['./plots-management.component.scss']
})
export class PlotsManagementComponent implements OnInit {
  plots: Plot[] = [];
  isLoading = false;

  constructor(
    private plotsService: PlotsService,
    private notificationService: NotificationService,
    private dialog: MatDialog,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadPlots();
  }

  loadPlots(): void {
    this.isLoading = true;
    
    this.plotsService.getMyPlots()
      .pipe(
        finalize(() => this.isLoading = false)
      )
      .subscribe({
        next: (plots) => {
          this.plots = plots;
        },
        error: (error) => {
          console.error('Error loading plots:', error);
          this.notificationService.showError('Failed to load plots');
        }
      });
  }

  openCreatePlotDialog(): void {
    const dialogRef = this.dialog.open(CreatePlotDialogComponent, {
      width: '600px',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadPlots();
        this.notificationService.showSuccess('Plot created successfully');
      }
    });
  }

  openEditPlotDialog(plot: Plot): void {
    const dialogRef = this.dialog.open(EditPlotDialogComponent, {
      width: '600px',
      data: { plot },
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadPlots();
        this.notificationService.showSuccess('Plot updated successfully');
      }
    });
  }

  viewPlotDetail(plot: Plot): void {
    this.router.navigate(['/user/plots', plot.id]);
  }

  deletePlot(plot: Plot): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Delete Plot',
        message: `Are you sure you want to delete "${plot.name}"? This action cannot be undone and will remove all associated parking slots.`,
        confirmText: 'Delete',
        type: 'danger'
      }
    });

    dialogRef.afterClosed().subscribe(confirmed => {
      if (confirmed) {
        this.plotsService.deletePlot(plot.id)
          .subscribe({
            next: () => {
              this.loadPlots();
              this.notificationService.showSuccess('Plot deleted successfully');
            },
            error: (error) => {
              console.error('Error deleting plot:', error);
              this.notificationService.showError('Failed to delete plot');
            }
          });
      }
    });
  }

  getOccupancyColor(plot: Plot): string {
    const rate = plot.occupancyRate || 0;
    if (rate >= 90) return 'text-red-600';
    if (rate >= 70) return 'text-yellow-600';
    return 'text-green-600';
  }

  getStatusColor(isActive: boolean): string {
    return isActive ? 'text-green-600' : 'text-red-600';
  }

  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }
}
