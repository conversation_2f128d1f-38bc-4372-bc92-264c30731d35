<div class="p-6 max-w-7xl mx-auto">
  <div *ngIf="isLoading" class="flex justify-center py-12">
    <app-loading-spinner size="large" message="Loading plot details..."></app-loading-spinner>
  </div>

  <div *ngIf="!isLoading && plot">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ plot.name }}</h1>
    <p class="text-gray-600 mb-8">{{ plot.location }}</p>
    
    <mat-card class="p-6">
      <h3 class="text-lg font-semibold mb-4">Plot Details</h3>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700">Type</label>
          <p class="text-gray-900">{{ plot.type }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Total Slots</label>
          <p class="text-gray-900">{{ plot.totalSlots }}</p>
        </div>
      </div>
    </mat-card>
  </div>
</div>
