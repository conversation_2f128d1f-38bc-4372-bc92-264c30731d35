import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { PlotsService, Plot } from '../../services/plots.service';

@Component({
  selector: 'app-plot-detail',
  templateUrl: './plot-detail.component.html',
  styleUrls: ['./plot-detail.component.scss']
})
export class PlotDetailComponent implements OnInit {
  plot: Plot | null = null;
  isLoading = true;

  constructor(
    private route: ActivatedRoute,
    private plotsService: PlotsService
  ) {}

  ngOnInit(): void {
    const plotId = Number(this.route.snapshot.paramMap.get('id'));
    this.loadPlot(plotId);
  }

  loadPlot(id: number): void {
    this.plotsService.getPlot(id).subscribe({
      next: (plot) => {
        this.plot = plot;
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
      }
    });
  }
}
