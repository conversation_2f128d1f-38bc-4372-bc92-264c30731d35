import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { finalize } from 'rxjs/operators';

import { PlotsService } from '../../services/plots.service';
import { NotificationService } from '../../../core/services/notification.service';

@Component({
  selector: 'app-create-plot-dialog',
  templateUrl: './create-plot-dialog.component.html',
  styleUrls: ['./create-plot-dialog.component.scss']
})
export class CreatePlotDialogComponent {
  plotForm: FormGroup;
  isLoading = false;

  plotTypes = [
    { value: 'Mall', label: 'Mall' },
    { value: 'Apartment', label: 'Apartment' },
    { value: 'Office', label: 'Office' },
    { value: 'Residential', label: 'Residential' }
  ];

  constructor(
    private formBuilder: FormBuilder,
    private plotsService: PlotsService,
    private notificationService: NotificationService,
    public dialogRef: MatDialogRef<CreatePlotDialogComponent>
  ) {
    this.plotForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      type: ['', [Validators.required]],
      location: ['', [Validators.required, Validators.minLength(5)]],
      totalSlots: ['', [Validators.required, Validators.min(1), Validators.max(1000)]],
      description: ['']
    });
  }

  onSubmit(): void {
    if (this.plotForm.valid && !this.isLoading) {
      this.isLoading = true;
      
      const plotData = this.plotForm.value;
      
      this.plotsService.createPlot(plotData)
        .pipe(
          finalize(() => this.isLoading = false)
        )
        .subscribe({
          next: (plot) => {
            this.dialogRef.close(plot);
          },
          error: (error) => {
            console.error('Error creating plot:', error);
            // Error notification is handled by the error interceptor
          }
        });
    } else {
      this.markFormGroupTouched();
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  private markFormGroupTouched(): void {
    Object.keys(this.plotForm.controls).forEach(key => {
      const control = this.plotForm.get(key);
      control?.markAsTouched();
    });
  }

  getErrorMessage(fieldName: string): string {
    const control = this.plotForm.get(fieldName);
    
    if (control?.hasError('required')) {
      return `${this.getFieldLabel(fieldName)} is required`;
    }
    
    if (control?.hasError('minlength')) {
      const requiredLength = control.errors?.['minlength']?.requiredLength;
      return `${this.getFieldLabel(fieldName)} must be at least ${requiredLength} characters long`;
    }
    
    if (control?.hasError('min')) {
      const min = control.errors?.['min']?.min;
      return `${this.getFieldLabel(fieldName)} must be at least ${min}`;
    }
    
    if (control?.hasError('max')) {
      const max = control.errors?.['max']?.max;
      return `${this.getFieldLabel(fieldName)} cannot exceed ${max}`;
    }
    
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      name: 'Plot name',
      type: 'Plot type',
      location: 'Location',
      totalSlots: 'Total slots',
      description: 'Description'
    };
    
    return labels[fieldName] || fieldName;
  }
}
