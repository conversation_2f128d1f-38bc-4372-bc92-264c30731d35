<div class="p-6 max-w-7xl mx-auto">
  <div class="flex items-center justify-between mb-8">
    <div>
      <h1 class="text-3xl font-bold text-gray-900">Guest Management</h1>
      <p class="mt-2 text-gray-600">Manage expected guests and their parking access</p>
    </div>
    <button mat-raised-button color="primary" (click)="openCreateDialog()">
      <mat-icon>person_add</mat-icon>
      Add Guest
    </button>
  </div>

  <div *ngIf="isLoading" class="flex justify-center py-12">
    <app-loading-spinner size="large" message="Loading guests..."></app-loading-spinner>
  </div>

  <div *ngIf="!isLoading && guests.length === 0" class="text-center py-12">
    <mat-card class="p-8">
      <mat-icon class="text-gray-400 text-6xl mb-4">people</mat-icon>
      <h3 class="text-xl font-medium text-gray-900 mb-2">No guests yet</h3>
      <p class="text-gray-600 mb-6">Add expected guests to manage their parking access.</p>
      <button mat-raised-button color="primary" (click)="openCreateDialog()">
        Add Your First Guest
      </button>
    </mat-card>
  </div>

  <div *ngIf="!isLoading && guests.length > 0" class="space-y-4">
    <mat-card *ngFor="let guest of guests" class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-semibold text-gray-900">{{ guest.name }}</h3>
          <p class="text-gray-600">{{ guest.vehicleNumber }}</p>
          <p class="text-sm text-gray-500">{{ guest.purpose }}</p>
        </div>
        <span class="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
          {{ guest.status }}
        </span>
      </div>
    </mat-card>
  </div>
</div>
