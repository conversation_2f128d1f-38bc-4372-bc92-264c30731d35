import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';

import { GuestsService, Guest } from '../services/guests.service';
import { CreateGuestDialogComponent } from './create-guest-dialog/create-guest-dialog.component';

@Component({
  selector: 'app-guests-management',
  templateUrl: './guests-management.component.html',
  styleUrls: ['./guests-management.component.scss']
})
export class GuestsManagementComponent implements OnInit {
  guests: Guest[] = [];
  isLoading = false;

  constructor(
    private guestsService: GuestsService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadGuests();
  }

  loadGuests(): void {
    this.isLoading = true;
    this.guestsService.getMyGuests().subscribe({
      next: (guests) => {
        this.guests = guests;
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
      }
    });
  }

  openCreateDialog(): void {
    const dialogRef = this.dialog.open(CreateGuestDialogComponent, {
      width: '500px'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadGuests();
      }
    });
  }
}
