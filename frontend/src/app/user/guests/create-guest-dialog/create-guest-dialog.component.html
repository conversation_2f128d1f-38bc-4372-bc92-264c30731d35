<div class="p-6">
  <h2 class="text-xl font-semibold text-gray-900 mb-6">Add Expected Guest</h2>
  
  <form [formGroup]="guestForm" (ngSubmit)="onSubmit()" class="space-y-4">
    <mat-form-field appearance="outline" class="w-full">
      <mat-label>Guest Name</mat-label>
      <input matInput formControlName="name">
    </mat-form-field>

    <mat-form-field appearance="outline" class="w-full">
      <mat-label>Contact</mat-label>
      <input matInput formControlName="contact" placeholder="Phone number">
    </mat-form-field>

    <mat-form-field appearance="outline" class="w-full">
      <mat-label>Vehicle Number</mat-label>
      <input matInput formControlName="vehicleNumber">
    </mat-form-field>

    <mat-form-field appearance="outline" class="w-full">
      <mat-label>Purpose of Visit</mat-label>
      <input matInput formControlName="purpose">
    </mat-form-field>

    <mat-form-field appearance="outline" class="w-full">
      <mat-label>Expected Visit Time</mat-label>
      <input matInput type="datetime-local" formControlName="expectedVisitTime">
    </mat-form-field>

    <div class="flex justify-end space-x-3 pt-4">
      <button type="button" mat-button (click)="onCancel()">Cancel</button>
      <button type="submit" mat-raised-button color="primary" [disabled]="isLoading">
        Add Guest
      </button>
    </div>
  </form>
</div>
