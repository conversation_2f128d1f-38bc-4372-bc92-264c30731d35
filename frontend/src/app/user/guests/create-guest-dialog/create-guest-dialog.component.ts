import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';

import { GuestsService } from '../../services/guests.service';

@Component({
  selector: 'app-create-guest-dialog',
  templateUrl: './create-guest-dialog.component.html',
  styleUrls: ['./create-guest-dialog.component.scss']
})
export class CreateGuestDialogComponent {
  guestForm: FormGroup;
  isLoading = false;

  constructor(
    private formBuilder: FormBuilder,
    private guestsService: GuestsService,
    public dialogRef: MatDialogRef<CreateGuestDialogComponent>
  ) {
    this.guestForm = this.formBuilder.group({
      name: ['', [Validators.required]],
      contact: ['', [Validators.required]],
      vehicleNumber: ['', [Validators.required]],
      purpose: ['', [Validators.required]],
      expectedVisitTime: ['', [Validators.required]]
    });
  }

  onSubmit(): void {
    if (this.guestForm.valid && !this.isLoading) {
      this.isLoading = true;
      
      this.guestsService.createGuest(this.guestForm.value).subscribe({
        next: (guest) => {
          this.dialogRef.close(guest);
        },
        error: () => {
          this.isLoading = false;
        }
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
