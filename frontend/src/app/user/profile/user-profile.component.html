<div class="p-6 max-w-4xl mx-auto">
  <div class="flex items-center justify-between mb-8">
    <div>
      <h1 class="text-3xl font-bold text-gray-900">My Profile</h1>
      <p class="mt-2 text-gray-600">Manage your account information</p>
    </div>
    <button
      *ngIf="!isEditing"
      mat-raised-button
      color="primary"
      (click)="toggleEdit()"
    >
      <mat-icon>edit</mat-icon>
      Edit Profile
    </button>
  </div>

  <mat-card class="p-6">
    <form [formGroup]="profileForm" (ngSubmit)="onSave()" class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <mat-form-field appearance="outline">
          <mat-label>First Name</mat-label>
          <input matInput formControlName="firstName" [readonly]="!isEditing">
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Last Name</mat-label>
          <input matInput formControlName="lastName" [readonly]="!isEditing">
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Email</mat-label>
          <input matInput formControlName="email" readonly>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Phone</mat-label>
          <input matInput formControlName="phone" [readonly]="!isEditing">
        </mat-form-field>
      </div>

      <div *ngIf="isEditing" class="flex justify-end space-x-3 pt-4 border-t">
        <button type="button" mat-button (click)="onCancel()">Cancel</button>
        <button type="submit" mat-raised-button color="primary" [disabled]="isLoading">
          Save Changes
        </button>
      </div>
    </form>
  </mat-card>
</div>
