<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div>
      <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-indigo-100">
        <mat-icon class="text-indigo-600">local_parking</mat-icon>
      </div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Parking Management System
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        Sign in to your account
      </p>
    </div>

    <mat-card class="mt-8">
      <mat-card-content class="p-6">
        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="space-y-6">
          <div>
            <mat-form-field appearance="outline" class="w-full">
              <mat-label>Email Address</mat-label>
              <input
                matInput
                type="email"
                formControlName="email"
                placeholder="Enter your email"
                autocomplete="email"
              >
              <mat-icon matSuffix>email</mat-icon>
              <mat-error *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">
                {{ getErrorMessage('email') }}
              </mat-error>
            </mat-form-field>
          </div>

          <div>
            <mat-form-field appearance="outline" class="w-full">
              <mat-label>Password</mat-label>
              <input
                matInput
                [type]="hidePassword ? 'password' : 'text'"
                formControlName="password"
                placeholder="Enter your password"
                autocomplete="current-password"
              >
              <button
                mat-icon-button
                matSuffix
                type="button"
                (click)="hidePassword = !hidePassword"
                [attr.aria-label]="'Hide password'"
                [attr.aria-pressed]="hidePassword"
              >
                <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
              </button>
              <mat-error *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
                {{ getErrorMessage('password') }}
              </mat-error>
            </mat-form-field>
          </div>

          <div>
            <button
              mat-raised-button
              color="primary"
              type="submit"
              class="w-full"
              [disabled]="isLoading"
            >
              <span *ngIf="!isLoading">Sign In</span>
              <span *ngIf="isLoading" class="flex items-center justify-center">
                <mat-spinner diameter="20" class="mr-2"></mat-spinner>
                Signing in...
              </span>
            </button>
          </div>
        </form>

        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500">Demo Credentials</span>
            </div>
          </div>

          <div class="mt-4 space-y-2 text-sm text-gray-600">
            <div class="bg-blue-50 p-3 rounded-md">
              <p class="font-medium text-blue-800">Admin Account:</p>
              <p>Email: admin&#64;parking.com</p>
              <p>Password: admin123</p>
            </div>
            <div class="bg-green-50 p-3 rounded-md">
              <p class="font-medium text-green-800">User Account:</p>
              <p>Email: john.doe&#64;example.com</p>
              <p>Password: admin123</p>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
