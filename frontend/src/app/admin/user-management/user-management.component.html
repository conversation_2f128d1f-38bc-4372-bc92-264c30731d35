<div class="p-6 max-w-7xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">User Management</h1>
        <p class="mt-2 text-gray-600">Manage system users and their permissions</p>
      </div>
      <button
        mat-raised-button
        color="primary"
        (click)="openCreateUserDialog()"
        class="flex items-center space-x-2"
      >
        <mat-icon>add</mat-icon>
        <span>Create User</span>
      </button>
    </div>
  </div>

  <!-- Search and Filters -->
  <mat-card class="mb-6">
    <mat-card-content class="p-4">
      <div class="flex items-center space-x-4">
        <mat-form-field appearance="outline" class="flex-1">
          <mat-label>Search users</mat-label>
          <input
            matInput
            placeholder="Search by name, email, or phone"
            (input)="onSearch($event)"
            [value]="searchTerm"
          >
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
        
        <button
          mat-icon-button
          (click)="loadUsers()"
          matTooltip="Refresh"
          [disabled]="isLoading"
        >
          <mat-icon>refresh</mat-icon>
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Users Table -->
  <mat-card>
    <mat-card-content class="p-0">
      <!-- Loading State -->
      <div *ngIf="isLoading" class="flex justify-center py-8">
        <app-loading-spinner size="medium" message="Loading users..."></app-loading-spinner>
      </div>

      <!-- Table -->
      <div *ngIf="!isLoading" class="overflow-x-auto">
        <table mat-table [dataSource]="dataSource" matSort class="w-full">
          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="font-semibold">Name</th>
            <td mat-cell *matCellDef="let user" class="py-4">
              <div class="flex items-center">
                <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                  <mat-icon class="text-indigo-600">person</mat-icon>
                </div>
                <div class="ml-3">
                  <p class="font-medium text-gray-900">{{ user.firstName }} {{ user.lastName }}</p>
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Email Column -->
          <ng-container matColumnDef="email">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="font-semibold">Email</th>
            <td mat-cell *matCellDef="let user" class="py-4">
              <span class="text-gray-900">{{ user.email }}</span>
            </td>
          </ng-container>

          <!-- Phone Column -->
          <ng-container matColumnDef="phone">
            <th mat-header-cell *matHeaderCellDef class="font-semibold">Phone</th>
            <td mat-cell *matCellDef="let user" class="py-4">
              <span class="text-gray-600">{{ user.phone || 'N/A' }}</span>
            </td>
          </ng-container>

          <!-- Role Column -->
          <ng-container matColumnDef="role">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="font-semibold">Role</th>
            <td mat-cell *matCellDef="let user" class="py-4">
              <span
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                [class]="user.role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'"
              >
                {{ user.role | titlecase }}
              </span>
            </td>
          </ng-container>

          <!-- Plots Count Column -->
          <ng-container matColumnDef="plotsCount">
            <th mat-header-cell *matHeaderCellDef class="font-semibold">Plots</th>
            <td mat-cell *matCellDef="let user" class="py-4">
              <span class="text-gray-900">{{ user.plots?.length || 0 }}</span>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="font-semibold">Status</th>
            <td mat-cell *matCellDef="let user" class="py-4">
              <span
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                [class]="user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
              >
                <mat-icon class="mr-1 text-xs">
                  {{ user.isActive ? 'check_circle' : 'cancel' }}
                </mat-icon>
                {{ getStatusText(user.isActive) }}
              </span>
            </td>
          </ng-container>

          <!-- Created Date Column -->
          <ng-container matColumnDef="createdAt">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="font-semibold">Created</th>
            <td mat-cell *matCellDef="let user" class="py-4">
              <span class="text-gray-600">{{ formatDate(user.createdAt) }}</span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef class="font-semibold">Actions</th>
            <td mat-cell *matCellDef="let user" class="py-4">
              <div class="flex items-center space-x-2">
                <button
                  mat-icon-button
                  (click)="openUserDetail(user)"
                  matTooltip="View Details"
                  class="text-blue-600 hover:text-blue-800"
                >
                  <mat-icon>visibility</mat-icon>
                </button>
                
                <button
                  mat-icon-button
                  (click)="toggleUserStatus(user)"
                  [matTooltip]="user.isActive ? 'Deactivate User' : 'Activate User'"
                  [class]="user.isActive ? 'text-orange-600 hover:text-orange-800' : 'text-green-600 hover:text-green-800'"
                >
                  <mat-icon>{{ user.isActive ? 'block' : 'check_circle' }}</mat-icon>
                </button>
                
                <button
                  mat-icon-button
                  (click)="deleteUser(user)"
                  matTooltip="Delete User"
                  class="text-red-600 hover:text-red-800"
                >
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="hover:bg-gray-50"></tr>
        </table>

        <!-- No Data State -->
        <div *ngIf="dataSource.data.length === 0 && !isLoading" class="text-center py-12">
          <mat-icon class="text-gray-400 text-6xl mb-4">people_outline</mat-icon>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No users found</h3>
          <p class="text-gray-600 mb-4">
            {{ searchTerm ? 'Try adjusting your search criteria' : 'Get started by creating your first user' }}
          </p>
          <button
            *ngIf="!searchTerm"
            mat-raised-button
            color="primary"
            (click)="openCreateUserDialog()"
          >
            Create User
          </button>
        </div>
      </div>

      <!-- Pagination -->
      <mat-paginator
        *ngIf="!isLoading && dataSource.data.length > 0"
        [length]="totalUsers"
        [pageSize]="pageSize"
        [pageIndex]="currentPage"
        [pageSizeOptions]="[5, 10, 25, 50]"
        (page)="onPageChange($event)"
        showFirstLastButtons
      ></mat-paginator>
    </mat-card-content>
  </mat-card>
</div>
