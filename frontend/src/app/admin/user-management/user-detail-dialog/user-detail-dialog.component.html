<div class="p-6">
  <!-- Header -->
  <div class="flex items-center justify-between mb-6">
    <h2 class="text-xl font-semibold text-gray-900">User Details</h2>
    <div class="flex items-center space-x-2">
      <button
        *ngIf="!isEditing"
        mat-button
        color="primary"
        (click)="toggleEdit()"
        class="flex items-center space-x-1"
      >
        <mat-icon>edit</mat-icon>
        <span>Edit</span>
      </button>
      
      <button
        *ngIf="isEditing"
        mat-button
        (click)="toggleEdit()"
        class="flex items-center space-x-1"
      >
        <mat-icon>cancel</mat-icon>
        <span>Cancel</span>
      </button>
      
      <button
        mat-icon-button
        (click)="onCancel()"
        class="text-gray-400 hover:text-gray-600"
      >
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>

  <!-- User Information Form -->
  <form [formGroup]="userForm" (ngSubmit)="onSave()" class="space-y-6">
    <!-- Basic Information -->
    <div class="bg-gray-50 p-4 rounded-lg">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <mat-form-field appearance="outline">
          <mat-label>First Name</mat-label>
          <input
            matInput
            formControlName="firstName"
            [readonly]="!isEditing"
          >
          <mat-error *ngIf="userForm.get('firstName')?.invalid && userForm.get('firstName')?.touched">
            {{ getErrorMessage('firstName') }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Last Name</mat-label>
          <input
            matInput
            formControlName="lastName"
            [readonly]="!isEditing"
          >
          <mat-error *ngIf="userForm.get('lastName')?.invalid && userForm.get('lastName')?.touched">
            {{ getErrorMessage('lastName') }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Email</mat-label>
          <input
            matInput
            formControlName="email"
            readonly
          >
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Phone</mat-label>
          <input
            matInput
            formControlName="phone"
            [readonly]="!isEditing"
          >
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Role</mat-label>
          <input
            matInput
            formControlName="role"
            readonly
          >
        </mat-form-field>

        <div class="flex items-center space-x-2">
          <mat-slide-toggle
            formControlName="isActive"
            [disabled]="!isEditing"
            [color]="userForm.get('isActive')?.value ? 'primary' : 'warn'"
          >
            {{ getStatusText(userForm.get('isActive')?.value) }}
          </mat-slide-toggle>
        </div>
      </div>
    </div>

    <!-- Account Information -->
    <div class="bg-gray-50 p-4 rounded-lg">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Account Information</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">User ID</label>
          <p class="text-sm text-gray-900">{{ data.user.id }}</p>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Created Date</label>
          <p class="text-sm text-gray-900">{{ formatDate(data.user.createdAt) }}</p>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
          <p class="text-sm text-gray-900">{{ formatDate(data.user.updatedAt) }}</p>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
            [class]="data.user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
          >
            {{ getStatusText(data.user.isActive) }}
          </span>
        </div>
      </div>
    </div>

    <!-- User's Plots -->
    <div class="bg-gray-50 p-4 rounded-lg">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Plots ({{ userPlots.length }})</h3>
        <button
          mat-icon-button
          (click)="loadUserPlots()"
          matTooltip="Refresh"
          [disabled]="loadingPlots"
        >
          <mat-icon>refresh</mat-icon>
        </button>
      </div>

      <!-- Loading Plots -->
      <div *ngIf="loadingPlots" class="flex justify-center py-4">
        <app-loading-spinner size="small" message="Loading plots..."></app-loading-spinner>
      </div>

      <!-- Plots List -->
      <div *ngIf="!loadingPlots && userPlots.length > 0" class="space-y-3">
        <div
          *ngFor="let plot of userPlots"
          class="bg-white p-4 rounded-lg border border-gray-200"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h4 class="font-medium text-gray-900">{{ plot.name }}</h4>
              <p class="text-sm text-gray-600">{{ plot.location }}</p>
              <div class="flex items-center space-x-4 mt-2">
                <span class="text-xs text-gray-500">
                  Type: {{ plot.type }}
                </span>
                <span class="text-xs text-gray-500">
                  Slots: {{ getPlotStatusSummary(plot) }}
                </span>
              </div>
            </div>
            
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">
                {{ getOccupancyRate(plot) | number:'1.0-0' }}%
              </div>
              <div class="text-xs text-gray-500">Occupied</div>
              
              <!-- Mini progress bar -->
              <div class="w-16 bg-gray-200 rounded-full h-2 mt-1">
                <div
                  class="h-2 rounded-full"
                  [class]="getOccupancyRate(plot) > 80 ? 'bg-red-500' : getOccupancyRate(plot) > 60 ? 'bg-yellow-500' : 'bg-green-500'"
                  [style.width.%]="getOccupancyRate(plot)"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Plots -->
      <div *ngIf="!loadingPlots && userPlots.length === 0" class="text-center py-8">
        <mat-icon class="text-gray-400 text-4xl mb-2">location_city</mat-icon>
        <p class="text-gray-500">No plots found for this user</p>
      </div>
    </div>

    <!-- Action Buttons -->
    <div *ngIf="isEditing" class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
      <button
        type="button"
        mat-button
        (click)="toggleEdit()"
        [disabled]="isLoading"
      >
        Cancel
      </button>
      
      <button
        type="submit"
        mat-raised-button
        color="primary"
        [disabled]="isLoading || userForm.invalid"
      >
        <span *ngIf="!isLoading">Save Changes</span>
        <span *ngIf="isLoading" class="flex items-center">
          <mat-spinner diameter="16" class="mr-2"></mat-spinner>
          Saving...
        </span>
      </button>
    </div>
  </form>
</div>
