:host {
  display: block;
  max-height: 80vh;
  overflow-y: auto;
}

.mat-mdc-form-field {
  width: 100%;
}

.mat-mdc-dialog-content {
  padding: 0;
  margin: 0;
}

.plot-card {
  transition: box-shadow 0.2s ease-in-out;
}

.plot-card:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar {
  transition: width 0.3s ease-in-out;
}

// Form styling
.readonly-field {
  background-color: #f9fafb;
}

.mat-mdc-slide-toggle {
  margin: 0;
}

// Responsive adjustments
@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
  
  .p-6 {
    padding: 1rem;
  }
  
  .p-4 {
    padding: 0.75rem;
  }
}
