import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { finalize } from 'rxjs/operators';

import { AdminService } from '../../services/admin.service';
import { NotificationService } from '../../../core/services/notification.service';
import { User } from '../../../core/models/user.model';

export interface UserDetailDialogData {
  user: User;
}

@Component({
  selector: 'app-user-detail-dialog',
  templateUrl: './user-detail-dialog.component.html',
  styleUrls: ['./user-detail-dialog.component.scss']
})
export class UserDetailDialogComponent implements OnInit {
  userForm: FormGroup;
  isLoading = false;
  isEditing = false;
  userPlots: any[] = [];
  loadingPlots = false;

  constructor(
    private formBuilder: FormBuilder,
    private adminService: AdminService,
    private notificationService: NotificationService,
    public dialogRef: MatDialogRef<UserDetailDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: UserDetailDialogData
  ) {
    this.userForm = this.formBuilder.group({
      firstName: [data.user.firstName, [Validators.required]],
      lastName: [data.user.lastName, [Validators.required]],
      email: [{ value: data.user.email, disabled: true }],
      phone: [data.user.phone],
      role: [{ value: data.user.role, disabled: true }],
      isActive: [data.user.isActive]
    });
  }

  ngOnInit(): void {
    this.loadUserPlots();
  }

  loadUserPlots(): void {
    this.loadingPlots = true;
    
    this.adminService.getUserPlots(this.data.user.id)
      .pipe(
        finalize(() => this.loadingPlots = false)
      )
      .subscribe({
        next: (plots) => {
          this.userPlots = plots;
        },
        error: (error) => {
          console.error('Error loading user plots:', error);
          this.notificationService.showError('Failed to load user plots');
        }
      });
  }

  toggleEdit(): void {
    this.isEditing = !this.isEditing;
    
    if (!this.isEditing) {
      // Reset form to original values
      this.userForm.patchValue({
        firstName: this.data.user.firstName,
        lastName: this.data.user.lastName,
        phone: this.data.user.phone,
        isActive: this.data.user.isActive
      });
    }
  }

  onSave(): void {
    if (this.userForm.valid && !this.isLoading) {
      this.isLoading = true;
      
      const updateData = {
        firstName: this.userForm.get('firstName')?.value,
        lastName: this.userForm.get('lastName')?.value,
        phone: this.userForm.get('phone')?.value,
        isActive: this.userForm.get('isActive')?.value
      };

      this.adminService.updateUser(this.data.user.id, updateData)
        .pipe(
          finalize(() => this.isLoading = false)
        )
        .subscribe({
          next: (updatedUser) => {
            this.data.user = updatedUser;
            this.isEditing = false;
            this.dialogRef.close('updated');
          },
          error: (error) => {
            console.error('Error updating user:', error);
            this.notificationService.showError('Failed to update user');
          }
        });
    } else {
      this.markFormGroupTouched();
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  private markFormGroupTouched(): void {
    Object.keys(this.userForm.controls).forEach(key => {
      const control = this.userForm.get(key);
      control?.markAsTouched();
    });
  }

  getErrorMessage(fieldName: string): string {
    const control = this.userForm.get(fieldName);
    
    if (control?.hasError('required')) {
      return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
    }
    
    return '';
  }

  getStatusColor(isActive: boolean): string {
    return isActive ? 'text-green-600' : 'text-red-600';
  }

  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  getRoleColor(role: string): string {
    return role === 'admin' ? 'text-purple-600' : 'text-blue-600';
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  getPlotStatusSummary(plot: any): string {
    const total = plot.parkingSlots?.length || 0;
    const occupied = plot.parkingSlots?.filter((slot: any) => slot.status === 'Occupied').length || 0;
    const available = total - occupied;
    
    return `${available}/${total} available`;
  }

  getOccupancyRate(plot: any): number {
    const total = plot.parkingSlots?.length || 0;
    if (total === 0) return 0;
    
    const occupied = plot.parkingSlots?.filter((slot: any) => slot.status === 'Occupied').length || 0;
    return (occupied / total) * 100;
  }
}
