:host {
  display: block;
  min-height: 100vh;
  background-color: #f9fafb;
}

.mat-mdc-card {
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.mat-mdc-table {
  background: transparent;
}

.mat-mdc-header-cell {
  background-color: #f9fafb;
  color: #374151;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
}

.mat-mdc-cell {
  border-bottom: 1px solid #f3f4f6;
}

.mat-mdc-row:hover {
  background-color: #f9fafb;
}

.mat-mdc-form-field {
  width: 100%;
}

.mat-mdc-icon-button {
  width: 36px;
  height: 36px;
  line-height: 36px;
}

.mat-mdc-icon-button mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

// Status badges
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-active {
  background-color: #dcfce7;
  color: #166534;
}

.status-inactive {
  background-color: #fee2e2;
  color: #991b1b;
}

// Role badges
.role-admin {
  background-color: #f3e8ff;
  color: #7c3aed;
}

.role-user {
  background-color: #dbeafe;
  color: #1d4ed8;
}

// Responsive adjustments
@media (max-width: 768px) {
  .mat-mdc-table {
    font-size: 14px;
  }
  
  .mat-mdc-header-cell,
  .mat-mdc-cell {
    padding: 8px;
  }
  
  .p-6 {
    padding: 1rem;
  }
}
