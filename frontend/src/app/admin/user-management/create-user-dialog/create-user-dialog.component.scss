:host {
  display: block;
  min-width: 500px;
  max-width: 600px;
}

.mat-mdc-form-field {
  width: 100%;
}

.mat-mdc-dialog-content {
  padding: 0;
  margin: 0;
}

// Role information styling
.role-info {
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 1rem;
}

.role-info h4 {
  color: #1e40af;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.role-info ul {
  color: #1e3a8a;
}

.role-info li {
  margin-bottom: 0.25rem;
}

// Form validation styling
.mat-mdc-form-field.mat-form-field-invalid .mat-mdc-text-field-wrapper {
  border-color: #ef4444;
}

// Responsive adjustments
@media (max-width: 768px) {
  :host {
    min-width: auto;
    max-width: 90vw;
  }
  
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
  
  .p-6 {
    padding: 1rem;
  }
}
