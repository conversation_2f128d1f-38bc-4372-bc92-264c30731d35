<div class="p-6">
  <!-- Header -->
  <div class="flex items-center justify-between mb-6">
    <h2 class="text-xl font-semibold text-gray-900">Create New User</h2>
    <button
      mat-icon-button
      (click)="onCancel()"
      class="text-gray-400 hover:text-gray-600"
    >
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Form -->
  <form [formGroup]="userForm" (ngSubmit)="onSubmit()" class="space-y-6">
    <!-- Basic Information -->
    <div class="space-y-4">
      <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <mat-form-field appearance="outline">
          <mat-label>First Name</mat-label>
          <input
            matInput
            formControlName="firstName"
            placeholder="Enter first name"
            autocomplete="given-name"
          >
          <mat-error *ngIf="userForm.get('firstName')?.invalid && userForm.get('firstName')?.touched">
            {{ getErrorMessage('firstName') }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Last Name</mat-label>
          <input
            matInput
            formControlName="lastName"
            placeholder="Enter last name"
            autocomplete="family-name"
          >
          <mat-error *ngIf="userForm.get('lastName')?.invalid && userForm.get('lastName')?.touched">
            {{ getErrorMessage('lastName') }}
          </mat-error>
        </mat-form-field>
      </div>

      <mat-form-field appearance="outline" class="w-full">
        <mat-label>Email Address</mat-label>
        <input
          matInput
          type="email"
          formControlName="email"
          placeholder="Enter email address"
          autocomplete="email"
        >
        <mat-icon matSuffix>email</mat-icon>
        <mat-error *ngIf="userForm.get('email')?.invalid && userForm.get('email')?.touched">
          {{ getErrorMessage('email') }}
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="w-full">
        <mat-label>Password</mat-label>
        <input
          matInput
          [type]="hidePassword ? 'password' : 'text'"
          formControlName="password"
          placeholder="Enter password"
          autocomplete="new-password"
        >
        <button
          mat-icon-button
          matSuffix
          type="button"
          (click)="hidePassword = !hidePassword"
          [attr.aria-label]="'Hide password'"
          [attr.aria-pressed]="hidePassword"
        >
          <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
        </button>
        <mat-error *ngIf="userForm.get('password')?.invalid && userForm.get('password')?.touched">
          {{ getErrorMessage('password') }}
        </mat-error>
        <mat-hint>Password must be at least 6 characters long</mat-hint>
      </mat-form-field>

      <mat-form-field appearance="outline" class="w-full">
        <mat-label>Phone Number (Optional)</mat-label>
        <input
          matInput
          type="tel"
          formControlName="phone"
          placeholder="Enter phone number"
          autocomplete="tel"
        >
        <mat-icon matSuffix>phone</mat-icon>
      </mat-form-field>

      <mat-form-field appearance="outline" class="w-full">
        <mat-label>Role</mat-label>
        <mat-select formControlName="role">
          <mat-option *ngFor="let role of roles" [value]="role.value">
            {{ role.label }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="userForm.get('role')?.invalid && userForm.get('role')?.touched">
          {{ getErrorMessage('role') }}
        </mat-error>
        <mat-hint>Select the user's role in the system</mat-hint>
      </mat-form-field>
    </div>

    <!-- Role Information -->
    <div class="bg-blue-50 p-4 rounded-lg">
      <h4 class="text-sm font-medium text-blue-900 mb-2">Role Permissions</h4>
      <div *ngIf="userForm.get('role')?.value === 'admin'" class="text-sm text-blue-800">
        <p class="font-medium mb-1">Admin users can:</p>
        <ul class="list-disc list-inside space-y-1">
          <li>Manage all users in the system</li>
          <li>View system-wide statistics and reports</li>
          <li>Access admin dashboard and tools</li>
          <li>View all plots and parking data (read-only)</li>
        </ul>
      </div>
      <div *ngIf="userForm.get('role')?.value === 'user'" class="text-sm text-blue-800">
        <p class="font-medium mb-1">Regular users can:</p>
        <ul class="list-disc list-inside space-y-1">
          <li>Manage their own plots and parking slots</li>
          <li>Register and manage their vehicles</li>
          <li>Assign vehicles to available slots</li>
          <li>Manage guest access and temporary assignments</li>
        </ul>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
      <button
        type="button"
        mat-button
        (click)="onCancel()"
        [disabled]="isLoading"
      >
        Cancel
      </button>
      
      <button
        type="submit"
        mat-raised-button
        color="primary"
        [disabled]="isLoading || userForm.invalid"
      >
        <span *ngIf="!isLoading">Create User</span>
        <span *ngIf="isLoading" class="flex items-center">
          <mat-spinner diameter="16" class="mr-2"></mat-spinner>
          Creating...
        </span>
      </button>
    </div>
  </form>
</div>
