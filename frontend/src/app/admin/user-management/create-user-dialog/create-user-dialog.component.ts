import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { finalize } from 'rxjs/operators';

import { AdminService } from '../../services/admin.service';
import { NotificationService } from '../../../core/services/notification.service';
import { UserRole } from '../../../core/models/user.model';

@Component({
  selector: 'app-create-user-dialog',
  templateUrl: './create-user-dialog.component.html',
  styleUrls: ['./create-user-dialog.component.scss']
})
export class CreateUserDialogComponent {
  userForm: FormGroup;
  isLoading = false;
  hidePassword = true;

  roles = [
    { value: UserRole.USER, label: 'User' },
    { value: UserRole.ADMIN, label: 'Admin' }
  ];

  constructor(
    private formBuilder: FormBuilder,
    private adminService: AdminService,
    private notificationService: NotificationService,
    public dialogRef: MatDialogRef<CreateUserDialogComponent>
  ) {
    this.userForm = this.formBuilder.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      phone: [''],
      role: [UserRole.USER, [Validators.required]]
    });
  }

  onSubmit(): void {
    if (this.userForm.valid && !this.isLoading) {
      this.isLoading = true;
      
      const userData = this.userForm.value;
      
      this.adminService.createUser(userData)
        .pipe(
          finalize(() => this.isLoading = false)
        )
        .subscribe({
          next: (user) => {
            this.dialogRef.close(user);
          },
          error: (error) => {
            console.error('Error creating user:', error);
            // Error notification is handled by the error interceptor
          }
        });
    } else {
      this.markFormGroupTouched();
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  private markFormGroupTouched(): void {
    Object.keys(this.userForm.controls).forEach(key => {
      const control = this.userForm.get(key);
      control?.markAsTouched();
    });
  }

  getErrorMessage(fieldName: string): string {
    const control = this.userForm.get(fieldName);
    
    if (control?.hasError('required')) {
      return `${this.getFieldLabel(fieldName)} is required`;
    }
    
    if (control?.hasError('email')) {
      return 'Please enter a valid email address';
    }
    
    if (control?.hasError('minlength')) {
      const requiredLength = control.errors?.['minlength']?.requiredLength;
      return `${this.getFieldLabel(fieldName)} must be at least ${requiredLength} characters long`;
    }
    
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      firstName: 'First name',
      lastName: 'Last name',
      email: 'Email',
      password: 'Password',
      phone: 'Phone',
      role: 'Role'
    };
    
    return labels[fieldName] || fieldName;
  }
}
