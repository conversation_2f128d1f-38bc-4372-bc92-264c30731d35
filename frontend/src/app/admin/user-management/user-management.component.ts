import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatDialog } from '@angular/material/dialog';
import { debounceTime, distinctUntilChanged, finalize } from 'rxjs/operators';
import { Subject } from 'rxjs';

import { AdminService } from '../services/admin.service';
import { NotificationService } from '../../core/services/notification.service';
import { User } from '../../core/models/user.model';
import { UserDetailDialogComponent } from './user-detail-dialog/user-detail-dialog.component';
import { CreateUserDialogComponent } from './create-user-dialog/create-user-dialog.component';
import { ConfirmDialogComponent } from '../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-user-management',
  templateUrl: './user-management.component.html',
  styleUrls: ['./user-management.component.scss']
})
export class UserManagementComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  displayedColumns: string[] = ['name', 'email', 'phone', 'role', 'plotsCount', 'status', 'createdAt', 'actions'];
  dataSource = new MatTableDataSource<User>();
  
  isLoading = false;
  searchTerm = '';
  totalUsers = 0;
  pageSize = 10;
  currentPage = 0;
  
  private searchSubject = new Subject<string>();

  constructor(
    private adminService: AdminService,
    private notificationService: NotificationService,
    private dialog: MatDialog
  ) {
    // Setup search debouncing
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(searchTerm => {
      this.searchTerm = searchTerm;
      this.currentPage = 0;
      this.loadUsers();
    });
  }

  ngOnInit(): void {
    this.loadUsers();
  }

  loadUsers(): void {
    this.isLoading = true;
    
    this.adminService.getUsers(this.currentPage + 1, this.pageSize, this.searchTerm)
      .pipe(
        finalize(() => this.isLoading = false)
      )
      .subscribe({
        next: (response) => {
          this.dataSource.data = response.data;
          this.totalUsers = response.total;
        },
        error: (error) => {
          console.error('Error loading users:', error);
          this.notificationService.showError('Failed to load users');
        }
      });
  }

  onSearch(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchSubject.next(target.value);
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadUsers();
  }

  openCreateUserDialog(): void {
    const dialogRef = this.dialog.open(CreateUserDialogComponent, {
      width: '500px',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadUsers();
        this.notificationService.showSuccess('User created successfully');
      }
    });
  }

  openUserDetail(user: User): void {
    const dialogRef = this.dialog.open(UserDetailDialogComponent, {
      width: '800px',
      data: { user }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'updated') {
        this.loadUsers();
        this.notificationService.showSuccess('User updated successfully');
      }
    });
  }

  toggleUserStatus(user: User): void {
    const action = user.isActive ? 'deactivate' : 'activate';
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: `${action.charAt(0).toUpperCase() + action.slice(1)} User`,
        message: `Are you sure you want to ${action} ${user.firstName} ${user.lastName}?`,
        confirmText: action.charAt(0).toUpperCase() + action.slice(1),
        type: user.isActive ? 'warning' : 'info'
      }
    });

    dialogRef.afterClosed().subscribe(confirmed => {
      if (confirmed) {
        this.adminService.updateUser(user.id, { isActive: !user.isActive })
          .subscribe({
            next: () => {
              this.loadUsers();
              this.notificationService.showSuccess(`User ${action}d successfully`);
            },
            error: (error) => {
              console.error(`Error ${action}ing user:`, error);
              this.notificationService.showError(`Failed to ${action} user`);
            }
          });
      }
    });
  }

  deleteUser(user: User): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Delete User',
        message: `Are you sure you want to delete ${user.firstName} ${user.lastName}? This action cannot be undone.`,
        confirmText: 'Delete',
        type: 'danger'
      }
    });

    dialogRef.afterClosed().subscribe(confirmed => {
      if (confirmed) {
        this.adminService.deleteUser(user.id)
          .subscribe({
            next: () => {
              this.loadUsers();
              this.notificationService.showSuccess('User deleted successfully');
            },
            error: (error) => {
              console.error('Error deleting user:', error);
              this.notificationService.showError('Failed to delete user');
            }
          });
      }
    });
  }

  getStatusColor(isActive: boolean): string {
    return isActive ? 'text-green-600' : 'text-red-600';
  }

  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  getRoleColor(role: string): string {
    return role === 'admin' ? 'text-purple-600' : 'text-blue-600';
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }
}
