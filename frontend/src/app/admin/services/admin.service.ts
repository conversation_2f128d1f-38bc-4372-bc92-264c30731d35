import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

import { environment } from '../../../environments/environment';
import { User } from '../../core/models/user.model';
import { PaginatedResponse } from '../../core/models/api-response.model';

export interface DashboardStats {
  totalUsers: number;
  totalPlots: number;
  totalSlots: number;
  occupiedSlots: number;
  availableSlots: number;
  occupancyRate: number;
  recentActivity: any[];
  monthlyStats: any;
}

export interface SystemOverview {
  plotsByType: { [key: string]: { count: number; totalSlots: number; occupiedSlots: number } };
  userStats: {
    total: number;
    active: number;
    inactive: number;
  };
}

export interface CreateUserRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role?: string;
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  phone?: string;
  isActive?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class AdminService {
  private readonly API_URL = `${environment.apiUrl}/api/admin`;
  private readonly USERS_API_URL = `${environment.apiUrl}/api/users`;

  constructor(private http: HttpClient) {}

  getDashboardStats(): Observable<DashboardStats> {
    return this.http.get<DashboardStats>(`${this.API_URL}/dashboard/stats`);
  }

  getSystemOverview(): Observable<SystemOverview> {
    return this.http.get<SystemOverview>(`${this.API_URL}/system-overview`);
  }

  getUsers(page: number = 1, limit: number = 10, search?: string): Observable<PaginatedResponse<User>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    return this.http.get<PaginatedResponse<User>>(`${this.USERS_API_URL}`, { params });
  }

  getUser(id: number): Observable<User> {
    return this.http.get<User>(`${this.USERS_API_URL}/${id}`);
  }

  createUser(userData: CreateUserRequest): Observable<User> {
    return this.http.post<User>(`${this.USERS_API_URL}`, userData);
  }

  updateUser(id: number, userData: UpdateUserRequest): Observable<User> {
    return this.http.patch<User>(`${this.USERS_API_URL}/${id}`, userData);
  }

  deleteUser(id: number): Observable<{ message: string }> {
    return this.http.delete<{ message: string }>(`${this.USERS_API_URL}/${id}`);
  }

  getUserPlots(userId: number): Observable<any[]> {
    return this.http.get<any[]>(`${this.API_URL}/users/${userId}/plots`);
  }

  getOccupancyReport(startDate?: string, endDate?: string): Observable<any> {
    let params = new HttpParams();
    
    if (startDate) {
      params = params.set('startDate', startDate);
    }
    
    if (endDate) {
      params = params.set('endDate', endDate);
    }

    return this.http.get<any>(`${this.API_URL}/reports/occupancy`, { params });
  }
}
