import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SharedModule } from '../shared/shared.module';
import { AdminRoutingModule } from './admin-routing.module';
import { AdminDashboardComponent } from './dashboard/admin-dashboard.component';
import { UserManagementComponent } from './user-management/user-management.component';
import { UserDetailDialogComponent } from './user-management/user-detail-dialog/user-detail-dialog.component';
import { CreateUserDialogComponent } from './user-management/create-user-dialog/create-user-dialog.component';
import { SystemOverviewComponent } from './system-overview/system-overview.component';
import { ReportsComponent } from './reports/reports.component';

@NgModule({
  declarations: [
    AdminDashboardComponent,
    UserManagementComponent,
    UserDetailDialogComponent,
    CreateUserDialogComponent,
    SystemOverviewComponent,
    ReportsComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    AdminRoutingModule
  ]
})
export class AdminModule { }
