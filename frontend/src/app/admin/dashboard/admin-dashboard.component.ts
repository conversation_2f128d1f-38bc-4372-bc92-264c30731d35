import { Component, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';

import { AdminService, DashboardStats } from '../services/admin.service';
import { NotificationService } from '../../core/services/notification.service';

@Component({
  selector: 'app-admin-dashboard',
  templateUrl: './admin-dashboard.component.html',
  styleUrls: ['./admin-dashboard.component.scss']
})
export class AdminDashboardComponent implements OnInit {
  stats: DashboardStats | null = null;
  isLoading = true;

  constructor(
    private adminService: AdminService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadDashboardStats();
  }

  loadDashboardStats(): void {
    this.isLoading = true;
    
    this.adminService.getDashboardStats()
      .pipe(
        finalize(() => this.isLoading = false)
      )
      .subscribe({
        next: (stats) => {
          this.stats = stats;
        },
        error: (error) => {
          console.error('Error loading dashboard stats:', error);
          this.notificationService.showError('Failed to load dashboard statistics');
        }
      });
  }

  refreshStats(): void {
    this.loadDashboardStats();
  }

  getOccupancyColor(): string {
    if (!this.stats) return 'text-gray-600';
    
    const rate = this.stats.occupancyRate;
    if (rate >= 90) return 'text-red-600';
    if (rate >= 70) return 'text-yellow-600';
    return 'text-green-600';
  }

  getOccupancyIcon(): string {
    if (!this.stats) return 'info';
    
    const rate = this.stats.occupancyRate;
    if (rate >= 90) return 'warning';
    if (rate >= 70) return 'schedule';
    return 'check_circle';
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  getActivityIcon(action: string): string {
    switch (action) {
      case 'Assigned':
        return 'local_parking';
      case 'Released':
        return 'exit_to_app';
      default:
        return 'info';
    }
  }

  getActivityColor(action: string): string {
    switch (action) {
      case 'Assigned':
        return 'text-green-600';
      case 'Released':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  }
}
