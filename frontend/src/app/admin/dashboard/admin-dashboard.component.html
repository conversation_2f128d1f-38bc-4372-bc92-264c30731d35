<div class="p-6 max-w-7xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <p class="mt-2 text-gray-600">System overview and key metrics</p>
      </div>
      <button
        mat-raised-button
        color="primary"
        (click)="refreshStats()"
        [disabled]="isLoading"
        class="flex items-center space-x-2"
      >
        <mat-icon>refresh</mat-icon>
        <span>Refresh</span>
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex justify-center py-12">
    <app-loading-spinner size="large" message="Loading dashboard statistics..."></app-loading-spinner>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!isLoading && stats" class="space-y-8">
    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Users -->
      <mat-card class="p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <mat-icon class="text-blue-600">people</mat-icon>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Users</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.totalUsers }}</p>
          </div>
        </div>
      </mat-card>

      <!-- Total Plots -->
      <mat-card class="p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <mat-icon class="text-green-600">location_city</mat-icon>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Plots</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.totalPlots }}</p>
          </div>
        </div>
      </mat-card>

      <!-- Total Slots -->
      <mat-card class="p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <mat-icon class="text-purple-600">local_parking</mat-icon>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Slots</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.totalSlots }}</p>
          </div>
        </div>
      </mat-card>

      <!-- Occupancy Rate -->
      <mat-card class="p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <mat-icon [class]="getOccupancyColor()">{{ getOccupancyIcon() }}</mat-icon>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Occupancy Rate</p>
            <p class="text-2xl font-bold" [class]="getOccupancyColor()">
              {{ stats.occupancyRate | number:'1.1-1' }}%
            </p>
          </div>
        </div>
      </mat-card>
    </div>

    <!-- Detailed Statistics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Parking Statistics -->
      <mat-card class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Parking Statistics</h3>
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-gray-600">Available Slots</span>
            <span class="font-semibold text-green-600">{{ stats.availableSlots }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600">Occupied Slots</span>
            <span class="font-semibold text-red-600">{{ stats.occupiedSlots }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600">Total Capacity</span>
            <span class="font-semibold text-gray-900">{{ stats.totalSlots }}</span>
          </div>
          
          <!-- Progress Bar -->
          <div class="mt-4">
            <div class="flex justify-between text-sm text-gray-600 mb-1">
              <span>Utilization</span>
              <span>{{ stats.occupancyRate | number:'1.1-1' }}%</span>
            </div>
            <mat-progress-bar
              mode="determinate"
              [value]="stats.occupancyRate"
              [color]="stats.occupancyRate > 80 ? 'warn' : 'primary'"
            ></mat-progress-bar>
          </div>
        </div>
      </mat-card>

      <!-- Monthly Statistics -->
      <mat-card class="p-6" *ngIf="stats.monthlyStats">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">This Month's Activity</h3>
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-gray-600">Total Assignments</span>
            <span class="font-semibold text-blue-600">{{ stats.monthlyStats.assignmentLogs }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600">Total Releases</span>
            <span class="font-semibold text-green-600">{{ stats.monthlyStats.releaseLogs }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600">Vehicle Logs</span>
            <span class="font-semibold text-purple-600">{{ stats.monthlyStats.vehicleLogs }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600">Guest Logs</span>
            <span class="font-semibold text-orange-600">{{ stats.monthlyStats.guestLogs }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600">Avg. Duration</span>
            <span class="font-semibold text-gray-900">
              {{ stats.monthlyStats.averageParkingDurationMinutes | number:'1.0-0' }} min
            </span>
          </div>
        </div>
      </mat-card>
    </div>

    <!-- Recent Activity -->
    <mat-card class="p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
      <div *ngIf="stats.recentActivity && stats.recentActivity.length > 0; else noActivity">
        <div class="space-y-3">
          <div
            *ngFor="let activity of stats.recentActivity"
            class="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg"
          >
            <div class="flex-shrink-0">
              <mat-icon [class]="getActivityColor(activity.action)">
                {{ getActivityIcon(activity.action) }}
              </mat-icon>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900">
                {{ activity.action }} - Slot {{ activity.slot?.slotNumber }}
              </p>
              <p class="text-sm text-gray-600">
                {{ activity.slot?.plot?.name }}
                <span *ngIf="activity.vehicle"> - {{ activity.vehicle.vehicleNumber }}</span>
                <span *ngIf="activity.guest"> - Guest: {{ activity.guest.name }}</span>
              </p>
            </div>
            <div class="flex-shrink-0">
              <p class="text-xs text-gray-500">
                {{ formatDate(activity.assignedAt) }}
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <ng-template #noActivity>
        <div class="text-center py-8">
          <mat-icon class="text-gray-400 text-4xl mb-2">inbox</mat-icon>
          <p class="text-gray-500">No recent activity</p>
        </div>
      </ng-template>
    </mat-card>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && !stats" class="text-center py-12">
    <mat-icon class="text-gray-400 text-6xl mb-4">error_outline</mat-icon>
    <h3 class="text-lg font-medium text-gray-900 mb-2">Failed to load dashboard</h3>
    <p class="text-gray-600 mb-4">There was an error loading the dashboard statistics.</p>
    <button mat-raised-button color="primary" (click)="refreshStats()">
      Try Again
    </button>
  </div>
</div>
