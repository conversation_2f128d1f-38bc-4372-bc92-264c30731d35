import { Component, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';

import { AdminService, SystemOverview } from '../services/admin.service';
import { NotificationService } from '../../core/services/notification.service';

@Component({
  selector: 'app-system-overview',
  templateUrl: './system-overview.component.html',
  styleUrls: ['./system-overview.component.scss']
})
export class SystemOverviewComponent implements OnInit {
  overview: SystemOverview | null = null;
  isLoading = true;

  constructor(
    private adminService: AdminService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadSystemOverview();
  }

  loadSystemOverview(): void {
    this.isLoading = true;
    
    this.adminService.getSystemOverview()
      .pipe(
        finalize(() => this.isLoading = false)
      )
      .subscribe({
        next: (overview) => {
          this.overview = overview;
        },
        error: (error) => {
          console.error('Error loading system overview:', error);
          this.notificationService.showError('Failed to load system overview');
        }
      });
  }

  getPlotTypeKeys(): string[] {
    return this.overview ? Object.keys(this.overview.plotsByType) : [];
  }

  getOccupancyRate(plotType: any): number {
    if (!plotType.totalSlots) return 0;
    return (plotType.occupiedSlots / plotType.totalSlots) * 100;
  }
}
