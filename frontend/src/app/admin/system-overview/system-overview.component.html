<div class="p-6 max-w-7xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">System Overview</h1>
        <p class="mt-2 text-gray-600">Comprehensive view of system performance and usage</p>
      </div>
      <button
        mat-raised-button
        color="primary"
        (click)="loadSystemOverview()"
        [disabled]="isLoading"
        class="flex items-center space-x-2"
      >
        <mat-icon>refresh</mat-icon>
        <span>Refresh</span>
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex justify-center py-12">
    <app-loading-spinner size="large" message="Loading system overview..."></app-loading-spinner>
  </div>

  <!-- Overview Content -->
  <div *ngIf="!isLoading && overview" class="space-y-8">
    <!-- User Statistics -->
    <mat-card class="p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">User Statistics</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="text-center">
          <div class="text-3xl font-bold text-blue-600">{{ overview.userStats.total }}</div>
          <div class="text-sm text-gray-600">Total Users</div>
        </div>
        <div class="text-center">
          <div class="text-3xl font-bold text-green-600">{{ overview.userStats.active }}</div>
          <div class="text-sm text-gray-600">Active Users</div>
        </div>
        <div class="text-center">
          <div class="text-3xl font-bold text-red-600">{{ overview.userStats.inactive }}</div>
          <div class="text-sm text-gray-600">Inactive Users</div>
        </div>
      </div>
    </mat-card>

    <!-- Plots by Type -->
    <mat-card class="p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Plots by Type</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div
          *ngFor="let plotType of getPlotTypeKeys()"
          class="bg-gray-50 p-4 rounded-lg"
        >
          <h4 class="font-medium text-gray-900 mb-2">{{ plotType }}</h4>
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Plots:</span>
              <span class="font-medium">{{ overview.plotsByType[plotType].count }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Total Slots:</span>
              <span class="font-medium">{{ overview.plotsByType[plotType].totalSlots }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Occupied:</span>
              <span class="font-medium">{{ overview.plotsByType[plotType].occupiedSlots }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Occupancy:</span>
              <span class="font-medium">{{ getOccupancyRate(overview.plotsByType[plotType]) | number:'1.1-1' }}%</span>
            </div>
            
            <!-- Progress bar -->
            <div class="mt-2">
              <mat-progress-bar
                mode="determinate"
                [value]="getOccupancyRate(overview.plotsByType[plotType])"
                [color]="getOccupancyRate(overview.plotsByType[plotType]) > 80 ? 'warn' : 'primary'"
              ></mat-progress-bar>
            </div>
          </div>
        </div>
      </div>
    </mat-card>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && !overview" class="text-center py-12">
    <mat-icon class="text-gray-400 text-6xl mb-4">error_outline</mat-icon>
    <h3 class="text-lg font-medium text-gray-900 mb-2">Failed to load system overview</h3>
    <p class="text-gray-600 mb-4">There was an error loading the system overview data.</p>
    <button mat-raised-button color="primary" (click)="loadSystemOverview()">
      Try Again
    </button>
  </div>
</div>
