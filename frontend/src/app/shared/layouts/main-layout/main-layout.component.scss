:host {
  display: block;
  height: 100vh;
}

.router-link-active {
  border-color: #4f46e5 !important;
  color: #111827 !important;
}

.mat-mdc-menu-panel {
  min-width: 200px;
}

.mat-mdc-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mat-mdc-menu-item mat-icon {
  margin-right: 0;
}

// Mobile menu animations
.mobile-menu-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.mobile-menu-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 150ms ease-out, transform 150ms ease-out;
}

.mobile-menu-leave {
  opacity: 1;
  transform: translateY(0);
}

.mobile-menu-leave-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 150ms ease-in, transform 150ms ease-in;
}
