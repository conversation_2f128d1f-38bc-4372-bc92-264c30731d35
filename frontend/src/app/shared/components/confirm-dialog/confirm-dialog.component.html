<div class="p-6">
  <div class="flex items-start space-x-4">
    <div class="flex-shrink-0">
      <mat-icon [class]="getIconClass()" class="text-2xl">
        {{ getIconName() }}
      </mat-icon>
    </div>
    
    <div class="flex-1">
      <h2 class="text-lg font-medium text-gray-900 mb-2">
        {{ data.title }}
      </h2>
      <p class="text-sm text-gray-600">
        {{ data.message }}
      </p>
    </div>
  </div>
</div>

<div class="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
  <button
    mat-button
    (click)="onCancel()"
    class="text-gray-700"
  >
    {{ data.cancelText }}
  </button>
  
  <button
    mat-raised-button
    [color]="getConfirmButtonClass()"
    (click)="onConfirm()"
  >
    {{ data.confirmText }}
  </button>
</div>
