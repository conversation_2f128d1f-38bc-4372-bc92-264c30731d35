:host {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
  pointer-events: none;
}

.notification {
  pointer-events: auto;
  animation: slideInRight 0.3s ease-out;
}

.notification.removing {
  animation: slideOutRight 0.3s ease-in;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.mat-mdc-icon-button {
  width: 24px;
  height: 24px;
  line-height: 24px;
}

.mat-mdc-icon-button mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}
