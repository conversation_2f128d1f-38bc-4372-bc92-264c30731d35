<div class="fixed top-4 right-4 z-50 space-y-2">
  <div
    *ngFor="let notification of notifications"
    [class]="getNotificationClass(notification.type)"
    class="max-w-sm w-full border rounded-lg shadow-lg p-4 flex items-start space-x-3 transform transition-all duration-300 ease-in-out"
  >
    <mat-icon class="flex-shrink-0 mt-0.5">
      {{ getNotificationIcon(notification.type) }}
    </mat-icon>

    <div class="flex-1 min-w-0">
      <p class="text-sm font-medium">{{ notification.message }}</p>
    </div>

    <button
      mat-icon-button
      (click)="removeNotification(notification.id)"
      class="flex-shrink-0 -mr-1 -mt-1"
      class="text-gray-400 hover:text-gray-600"
    >
      <mat-icon class="text-sm">close</mat-icon>
    </button>
  </div>
</div>
