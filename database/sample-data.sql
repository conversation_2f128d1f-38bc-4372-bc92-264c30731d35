-- Sample data for Parking Management System

-- Insert sample users
INSERT INTO `users` (`email`, `password`, `firstName`, `lastName`, `phone`, `role`, `isActive`) VALUES
('<EMAIL>', '$2b$10$YourHashedPasswordHere', 'Admin', 'User', '+1234567890', 'admin', 1),
('<EMAIL>', '$2b$10$YourHashedPasswordHere', '<PERSON>', 'Doe', '+1234567891', 'user', 1),
('<EMAIL>', '$2b$10$YourHashedPasswordHere', '<PERSON>', '<PERSON>', '+1234567892', 'user', 1),
('<EMAIL>', '$2b$10$YourHashedPasswordHere', 'Mike', 'Wilson', '+1234567893', 'user', 1),
('<EMAIL>', '$2b$10$YourHashedPasswordHere', '<PERSON>', '<PERSON>', '+1234567894', 'user', 1);

-- Insert sample plots
INSERT INTO `plots` (`name`, `type`, `location`, `totalSlots`, `description`, `isActive`, `ownerId`) VALUES
('Downtown Mall Parking', 'Mall', '123 Main Street, Downtown City', 100, 'Main parking facility for downtown shopping mall', 1, 2),
('Sunset Apartments', 'Apartment', '456 Sunset Boulevard, Residential Area', 50, 'Resident parking for Sunset Apartments complex', 1, 2),
('Tech Office Complex', 'Office', '789 Innovation Drive, Business District', 75, 'Employee parking for tech companies', 1, 3),
('Green Valley Residential', 'Residential', '321 Green Valley Road, Suburbs', 30, 'Community parking for residential area', 1, 3),
('City Center Plaza', 'Office', '654 Business Avenue, City Center', 120, 'Premium office building parking', 1, 4);

-- Insert sample vehicles
INSERT INTO `vehicles` (`vehicleNumber`, `ownerName`, `type`, `contact`, `isActive`, `ownerId`) VALUES
('ABC-1234', 'John Doe', 'Car', '+1234567891', 1, 2),
('XYZ-5678', 'John Doe', 'Motorcycle', '+1234567891', 1, 2),
('DEF-9012', 'Jane Smith', 'Car', '+1234567892', 1, 3),
('GHI-3456', 'Jane Smith', 'Van', '+1234567892', 1, 3),
('JKL-7890', 'Mike Wilson', 'Car', '+1234567893', 1, 4),
('MNO-2468', 'Mike Wilson', 'Truck', '+1234567893', 1, 4),
('PQR-1357', 'Sarah Brown', 'Car', '+1234567894', 1, 5),
('STU-9753', 'Sarah Brown', 'Motorcycle', '+1234567894', 1, 5);

-- Insert sample guests
INSERT INTO `guests` (`name`, `contact`, `vehicleNumber`, `purpose`, `expectedVisitTime`, `status`, `hostId`) VALUES
('Alice Johnson', '+1555123456', 'GUEST-001', 'Business Meeting', '2024-01-15 14:00:00', 'Expected', 2),
('Bob Miller', '+1555234567', 'GUEST-002', 'Family Visit', '2024-01-15 16:30:00', 'Expected', 3),
('Carol Davis', '+1555345678', 'GUEST-003', 'Delivery Service', '2024-01-15 10:00:00', 'Arrived', 4),
('David Lee', '+1555456789', 'GUEST-004', 'Maintenance Work', '2024-01-15 09:00:00', 'Departed', 5),
('Emma White', '+1555567890', 'GUEST-005', 'Client Meeting', '2024-01-16 11:00:00', 'Expected', 2);

-- Generate parking slots for each plot
-- For Downtown Mall Parking (Plot ID 1) - 100 slots
INSERT INTO `parking_slots` (`slotNumber`, `status`, `plotId`) 
SELECT 
    CONCAT('A', LPAD(n, 3, '0')) as slotNumber,
    CASE 
        WHEN n <= 25 THEN 'Occupied'
        WHEN n <= 95 THEN 'Available'
        ELSE 'Maintenance'
    END as status,
    1 as plotId
FROM (
    SELECT a.N + b.N * 10 + 1 n
    FROM 
    (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) a,
    (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) b
    ORDER BY n
) numbers
WHERE n <= 100;

-- For Sunset Apartments (Plot ID 2) - 50 slots
INSERT INTO `parking_slots` (`slotNumber`, `status`, `plotId`) 
SELECT 
    CONCAT('B', LPAD(n, 3, '0')) as slotNumber,
    CASE 
        WHEN n <= 15 THEN 'Occupied'
        WHEN n <= 47 THEN 'Available'
        ELSE 'Maintenance'
    END as status,
    2 as plotId
FROM (
    SELECT a.N + b.N * 10 + 1 n
    FROM 
    (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) a,
    (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) b
    ORDER BY n
) numbers
WHERE n <= 50;

-- For Tech Office Complex (Plot ID 3) - 75 slots
INSERT INTO `parking_slots` (`slotNumber`, `status`, `plotId`) 
SELECT 
    CONCAT('C', LPAD(n, 3, '0')) as slotNumber,
    CASE 
        WHEN n <= 20 THEN 'Occupied'
        WHEN n <= 70 THEN 'Available'
        ELSE 'Maintenance'
    END as status,
    3 as plotId
FROM (
    SELECT a.N + b.N * 10 + 1 n
    FROM 
    (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) a,
    (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) b
    ORDER BY n
) numbers
WHERE n <= 75;

-- For Green Valley Residential (Plot ID 4) - 30 slots
INSERT INTO `parking_slots` (`slotNumber`, `status`, `plotId`) 
SELECT 
    CONCAT('D', LPAD(n, 3, '0')) as slotNumber,
    CASE 
        WHEN n <= 8 THEN 'Occupied'
        WHEN n <= 28 THEN 'Available'
        ELSE 'Maintenance'
    END as status,
    4 as plotId
FROM (
    SELECT a.N + b.N * 10 + 1 n
    FROM 
    (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) a,
    (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) b
    ORDER BY n
) numbers
WHERE n <= 30;

-- For City Center Plaza (Plot ID 5) - 120 slots
INSERT INTO `parking_slots` (`slotNumber`, `status`, `plotId`) 
SELECT 
    CONCAT('E', LPAD(n, 3, '0')) as slotNumber,
    CASE 
        WHEN n <= 30 THEN 'Occupied'
        WHEN n <= 115 THEN 'Available'
        ELSE 'Maintenance'
    END as status,
    5 as plotId
FROM (
    SELECT a.N + b.N * 10 + c.N * 100 + 1 n
    FROM 
    (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) a,
    (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) b,
    (SELECT 0 AS N UNION ALL SELECT 1) c
    ORDER BY n
) numbers
WHERE n <= 120;

-- Assign some vehicles to occupied slots
UPDATE `parking_slots` SET `vehicleId` = 1, `allocatedAt` = NOW() WHERE `plotId` = 1 AND `status` = 'Occupied' AND `slotNumber` = 'A001';
UPDATE `parking_slots` SET `vehicleId` = 3, `allocatedAt` = NOW() WHERE `plotId` = 2 AND `status` = 'Occupied' AND `slotNumber` = 'B001';
UPDATE `parking_slots` SET `vehicleId` = 5, `allocatedAt` = NOW() WHERE `plotId` = 3 AND `status` = 'Occupied' AND `slotNumber` = 'C001';
UPDATE `parking_slots` SET `vehicleId` = 7, `allocatedAt` = NOW() WHERE `plotId` = 4 AND `status` = 'Occupied' AND `slotNumber` = 'D001';

-- Insert sample parking logs
INSERT INTO `parking_logs` (`action`, `timestamp`, `notes`, `slotId`, `vehicleId`, `userId`) VALUES
('Assigned', '2024-01-15 08:00:00', 'Regular parking assignment', 1, 1, 2),
('Assigned', '2024-01-15 08:30:00', 'Morning shift parking', 51, 3, 3),
('Assigned', '2024-01-15 09:00:00', 'Employee parking', 151, 5, 4),
('Assigned', '2024-01-15 09:30:00', 'Resident parking', 226, 7, 5),
('Released', '2024-01-14 18:00:00', 'End of work day', 2, 2, 2),
('Released', '2024-01-14 17:30:00', 'Early departure', 52, 4, 3);
