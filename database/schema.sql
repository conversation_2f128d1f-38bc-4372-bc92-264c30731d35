-- Parking Management System Database Schema

-- Create users table
CREATE TABLE IF NOT EXISTS `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `firstName` varchar(100) NOT NULL,
  `lastName` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `role` enum('admin','user') NOT NULL DEFAULT 'user',
  `isActive` tinyint(1) NOT NULL DEFAULT '1',
  `refreshToken` text,
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_97672ac88f789774dd47f7c8be` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Create plots table
CREATE TABLE IF NOT EXISTS `plots` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` enum('Mall','Apartment','Office','Residential') NOT NULL,
  `location` text NOT NULL,
  `totalSlots` int NOT NULL,
  `description` text,
  `isActive` tinyint(1) NOT NULL DEFAULT '1',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ownerId` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_plots_owner` (`ownerId`),
  CONSTRAINT `FK_plots_owner` FOREIGN KEY (`ownerId`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Create vehicles table
CREATE TABLE IF NOT EXISTS `vehicles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `vehicleNumber` varchar(50) NOT NULL,
  `ownerName` varchar(255) NOT NULL,
  `type` enum('Car','Motorcycle','Truck','Van') NOT NULL,
  `contact` varchar(20) NOT NULL,
  `isActive` tinyint(1) NOT NULL DEFAULT '1',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ownerId` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_vehicles_number` (`vehicleNumber`),
  KEY `FK_vehicles_owner` (`ownerId`),
  CONSTRAINT `FK_vehicles_owner` FOREIGN KEY (`ownerId`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Create guests table
CREATE TABLE IF NOT EXISTS `guests` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `contact` varchar(20) NOT NULL,
  `vehicleNumber` varchar(50) NOT NULL,
  `purpose` varchar(255) NOT NULL,
  `expectedVisitTime` timestamp NOT NULL,
  `actualArrivalTime` timestamp NULL DEFAULT NULL,
  `actualDepartureTime` timestamp NULL DEFAULT NULL,
  `status` enum('Expected','Arrived','Departed') NOT NULL DEFAULT 'Expected',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `hostId` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_guests_host` (`hostId`),
  CONSTRAINT `FK_guests_host` FOREIGN KEY (`hostId`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Create parking_slots table
CREATE TABLE IF NOT EXISTS `parking_slots` (
  `id` int NOT NULL AUTO_INCREMENT,
  `slotNumber` varchar(10) NOT NULL,
  `status` enum('Available','Occupied','Maintenance') NOT NULL DEFAULT 'Available',
  `allocatedAt` timestamp NULL DEFAULT NULL,
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `plotId` int DEFAULT NULL,
  `vehicleId` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_parking_slots_plot` (`plotId`),
  KEY `FK_parking_slots_vehicle` (`vehicleId`),
  CONSTRAINT `FK_parking_slots_plot` FOREIGN KEY (`plotId`) REFERENCES `plots` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_parking_slots_vehicle` FOREIGN KEY (`vehicleId`) REFERENCES `vehicles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Create parking_logs table
CREATE TABLE IF NOT EXISTS `parking_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `action` enum('Assigned','Released','Updated') NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `notes` text,
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `slotId` int DEFAULT NULL,
  `vehicleId` int DEFAULT NULL,
  `guestId` int DEFAULT NULL,
  `userId` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_parking_logs_slot` (`slotId`),
  KEY `FK_parking_logs_vehicle` (`vehicleId`),
  KEY `FK_parking_logs_guest` (`guestId`),
  KEY `FK_parking_logs_user` (`userId`),
  CONSTRAINT `FK_parking_logs_guest` FOREIGN KEY (`guestId`) REFERENCES `guests` (`id`) ON DELETE SET NULL,
  CONSTRAINT `FK_parking_logs_slot` FOREIGN KEY (`slotId`) REFERENCES `parking_slots` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_parking_logs_user` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `FK_parking_logs_vehicle` FOREIGN KEY (`vehicleId`) REFERENCES `vehicles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
