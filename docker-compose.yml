version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: parking_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: parking_management
      MYSQL_USER: parking_user
      MYSQL_PASSWORD: parking_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./database/sample-data.sql:/docker-entrypoint-initdb.d/02-sample-data.sql
    networks:
      - parking_network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: parking_backend
    restart: unless-stopped
    environment:
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USERNAME: parking_user
      DB_PASSWORD: parking_password
      DB_DATABASE: parking_management
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production-12345
      JWT_EXPIRES_IN: 15m
      JWT_REFRESH_SECRET: your-super-secret-refresh-key-change-this-in-production-67890
      JWT_REFRESH_EXPIRES_IN: 7d
      PORT: 3000
      NODE_ENV: production
      FRONTEND_URL: http://localhost:4200
    ports:
      - "3000:3000"
    depends_on:
      - mysql
    networks:
      - parking_network
    volumes:
      - ./backend:/app
      - /app/node_modules

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: parking_frontend
    restart: unless-stopped
    ports:
      - "4200:80"
    depends_on:
      - backend
    networks:
      - parking_network

volumes:
  mysql_data:

networks:
  parking_network:
    driver: bridge
