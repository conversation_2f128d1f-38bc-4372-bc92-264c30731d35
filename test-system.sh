#!/bin/bash

# Parking Management System - System Test Script
echo "🧪 Testing Parking Management System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Function to print test result
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ $2${NC}"
        ((TESTS_FAILED++))
    fi
}

# Function to test HTTP endpoint
test_endpoint() {
    local url=$1
    local expected_status=$2
    local description=$3
    local auth_header=$4
    
    if [ -n "$auth_header" ]; then
        response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $auth_header" "$url")
    else
        response=$(curl -s -w "%{http_code}" "$url")
    fi
    
    status_code="${response: -3}"
    
    if [ "$status_code" = "$expected_status" ]; then
        print_result 0 "$description"
    else
        print_result 1 "$description (Expected: $expected_status, Got: $status_code)"
    fi
}

echo "🔍 Starting system tests..."
echo ""

# Test 1: Check if backend is running
echo "📡 Testing Backend Connectivity..."
test_endpoint "http://localhost:3000/health" "200" "Backend health check"

# Test 2: Check if frontend is accessible
echo ""
echo "🎨 Testing Frontend Connectivity..."
test_endpoint "http://localhost:4200" "200" "Frontend accessibility"

# Test 3: Database connectivity (through backend)
echo ""
echo "🗄️ Testing Database Connectivity..."

# Test login endpoint to verify database connection
echo ""
echo "🔐 Testing Authentication..."

# Test login with admin credentials
login_response=$(curl -s -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$login_response" | grep -q "accessToken"; then
    print_result 0 "Admin login successful"
    
    # Extract token for further tests
    admin_token=$(echo "$login_response" | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)
    
    # Test authenticated endpoints
    echo ""
    echo "🔒 Testing Authenticated Endpoints..."
    test_endpoint "http://localhost:3000/api/auth/profile" "200" "Get user profile" "$admin_token"
    test_endpoint "http://localhost:3000/api/admin/dashboard/stats" "200" "Admin dashboard stats" "$admin_token"
    test_endpoint "http://localhost:3000/api/users" "200" "User management endpoint" "$admin_token"
    
else
    print_result 1 "Admin login failed"
fi

# Test user login
user_login_response=$(curl -s -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$user_login_response" | grep -q "accessToken"; then
    print_result 0 "User login successful"
    
    # Extract user token
    user_token=$(echo "$user_login_response" | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)
    
    # Test user endpoints
    echo ""
    echo "👤 Testing User Endpoints..."
    test_endpoint "http://localhost:3000/api/plots/my-plots" "200" "Get user plots" "$user_token"
    test_endpoint "http://localhost:3000/api/vehicles/my-vehicles" "200" "Get user vehicles" "$user_token"
    test_endpoint "http://localhost:3000/api/guests/my-guests" "200" "Get user guests" "$user_token"
    
else
    print_result 1 "User login failed"
fi

# Test API endpoints without authentication (should fail)
echo ""
echo "🚫 Testing Unauthorized Access..."
test_endpoint "http://localhost:3000/api/users" "401" "Unauthorized access to admin endpoint"
test_endpoint "http://localhost:3000/api/plots/my-plots" "401" "Unauthorized access to user endpoint"

# Test CORS
echo ""
echo "🌐 Testing CORS Configuration..."
cors_response=$(curl -s -H "Origin: http://localhost:4200" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: authorization" \
  -X OPTIONS http://localhost:3000/api/auth/profile)

if [ $? -eq 0 ]; then
    print_result 0 "CORS preflight request successful"
else
    print_result 1 "CORS preflight request failed"
fi

# Test database operations
echo ""
echo "💾 Testing Database Operations..."

if [ -n "$admin_token" ]; then
    # Test creating a user
    create_user_response=$(curl -s -X POST http://localhost:3000/api/users \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $admin_token" \
      -d '{
        "email":"<EMAIL>",
        "password":"test123",
        "firstName":"Test",
        "lastName":"User",
        "role":"user"
      }')
    
    if echo "$create_user_response" | grep -q '"id"'; then
        print_result 0 "User creation successful"
        
        # Extract user ID for cleanup
        user_id=$(echo "$create_user_response" | grep -o '"id":[0-9]*' | cut -d':' -f2)
        
        # Test updating the user
        update_response=$(curl -s -X PATCH http://localhost:3000/api/users/$user_id \
          -H "Content-Type: application/json" \
          -H "Authorization: Bearer $admin_token" \
          -d '{"firstName":"Updated"}')
        
        if echo "$update_response" | grep -q '"firstName":"Updated"'; then
            print_result 0 "User update successful"
        else
            print_result 1 "User update failed"
        fi
        
        # Clean up - delete the test user
        delete_response=$(curl -s -X DELETE http://localhost:3000/api/users/$user_id \
          -H "Authorization: Bearer $admin_token")
        
        if [ $? -eq 0 ]; then
            print_result 0 "User deletion successful"
        else
            print_result 1 "User deletion failed"
        fi
    else
        print_result 1 "User creation failed"
    fi
fi

# Test plot operations for regular user
echo ""
echo "🏢 Testing Plot Management..."

if [ -n "$user_token" ]; then
    # Test creating a plot
    create_plot_response=$(curl -s -X POST http://localhost:3000/api/plots \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $user_token" \
      -d '{
        "name":"Test Parking Plot",
        "type":"Office",
        "location":"123 Test Street",
        "totalSlots":10,
        "description":"Test plot for system testing"
      }')
    
    if echo "$create_plot_response" | grep -q '"id"'; then
        print_result 0 "Plot creation successful"
        
        # Extract plot ID
        plot_id=$(echo "$create_plot_response" | grep -o '"id":[0-9]*' | cut -d':' -f2)
        
        # Test getting plot details
        plot_details=$(curl -s -H "Authorization: Bearer $user_token" \
          "http://localhost:3000/api/plots/$plot_id")
        
        if echo "$plot_details" | grep -q '"parkingSlots"'; then
            print_result 0 "Plot details retrieval successful"
        else
            print_result 1 "Plot details retrieval failed"
        fi
        
        # Clean up - delete the test plot
        curl -s -X DELETE http://localhost:3000/api/plots/$plot_id \
          -H "Authorization: Bearer $user_token" > /dev/null
        
        print_result 0 "Plot cleanup successful"
    else
        print_result 1 "Plot creation failed"
    fi
fi

# Performance test
echo ""
echo "⚡ Testing Performance..."

start_time=$(date +%s%N)
for i in {1..10}; do
    curl -s http://localhost:3000/health > /dev/null
done
end_time=$(date +%s%N)

duration=$(( (end_time - start_time) / 1000000 ))
avg_response_time=$(( duration / 10 ))

if [ $avg_response_time -lt 100 ]; then
    print_result 0 "Performance test passed (avg: ${avg_response_time}ms)"
else
    print_result 1 "Performance test failed (avg: ${avg_response_time}ms, expected < 100ms)"
fi

# Summary
echo ""
echo "📊 Test Summary:"
echo "=================="
echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests Failed: ${RED}$TESTS_FAILED${NC}"
echo -e "Total Tests: $(($TESTS_PASSED + $TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 All tests passed! The system is working correctly.${NC}"
    exit 0
else
    echo ""
    echo -e "${RED}⚠️  Some tests failed. Please check the system configuration.${NC}"
    exit 1
fi
