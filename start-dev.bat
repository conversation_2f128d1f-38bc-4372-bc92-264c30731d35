@echo off
title Parking Management System - Development Server

echo 🚗 Starting Parking Management System...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ and try again.
    pause
    exit /b 1
)

echo 🔍 Checking Node.js version...
node --version

echo.
echo 🚀 Starting backend server...
cd backend

REM Check if node_modules exists
if not exist "node_modules" (
    echo 📦 Installing backend dependencies...
    call npm install
)

REM Check if .env exists
if not exist ".env" (
    echo ⚙️ Creating .env file from .env.example...
    copy .env.example .env
    echo ✏️ Please edit backend/.env with your database credentials
)

REM Start backend
start "Backend Server" cmd /k "npm run start:dev"

echo ✅ Backend server starting...
echo.

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

echo 🎨 Starting frontend application...
cd ..\frontend

REM Check if node_modules exists
if not exist "node_modules" (
    echo 📦 Installing frontend dependencies...
    call npm install
)

REM Start frontend
start "Frontend Server" cmd /k "ng serve"

echo ✅ Frontend server starting...
echo.

echo 🎉 Parking Management System is starting up!
echo.
echo 📱 Frontend: http://localhost:4200
echo 🔧 Backend API: http://localhost:3000
echo.
echo 🔑 Default Login Credentials:
echo    Admin: <EMAIL> / admin123
echo    User:  <EMAIL> / admin123
echo.
echo ⏹️ Close the terminal windows to stop the servers
echo.

pause
