import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ParkingLogsService } from './parking-logs.service';
import { ParkingLogsController } from './parking-logs.controller';
import { ParkingLog } from './entities/parking-log.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ParkingLog])],
  controllers: [ParkingLogsController],
  providers: [ParkingLogsService],
  exports: [ParkingLogsService],
})
export class ParkingLogsModule {}
