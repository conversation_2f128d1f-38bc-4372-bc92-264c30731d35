import {
  Controller,
  Get,
  Param,
  UseGuards,
  Query,
  ParseIntPipe,
} from '@nestjs/common';

import { ParkingLogsService, LogFilterOptions } from './parking-logs.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User, UserRole } from '../users/entities/user.entity';

@Controller('parking-logs')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ParkingLogsController {
  constructor(private readonly parkingLogsService: ParkingLogsService) {}

  @Get()
  async findAll(
    @Query('page', ParseIntPipe) page?: number,
    @Query('limit', ParseIntPipe) limit?: number,
    @Query('search') search?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('plotId', ParseIntPipe) plotId?: number,
    @Query('vehicleId', ParseIntPipe) vehicleId?: number,
    @Query('guestId', ParseIntPipe) guestId?: number,
    @Query('action') action?: string,
    @CurrentUser() user?: User,
  ) {
    const options: LogFilterOptions = {
      page,
      limit,
      search,
      startDate,
      endDate,
      plotId,
      vehicleId,
      guestId,
      action,
    };
    return this.parkingLogsService.findAll(options, user);
  }

  @Get('my-logs')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async findMyLogs(
    @Query('page', ParseIntPipe) page?: number,
    @Query('limit', ParseIntPipe) limit?: number,
    @Query('search') search?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('plotId', ParseIntPipe) plotId?: number,
    @Query('vehicleId', ParseIntPipe) vehicleId?: number,
    @Query('guestId', ParseIntPipe) guestId?: number,
    @Query('action') action?: string,
    @CurrentUser() user: User,
  ) {
    const options: LogFilterOptions = {
      page,
      limit,
      search,
      startDate,
      endDate,
      plotId,
      vehicleId,
      guestId,
      action,
    };
    return this.parkingLogsService.findUserLogs(user.id, options);
  }

  @Get('statistics')
  async getStatistics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @CurrentUser() user?: User,
  ) {
    const userId = user?.role === UserRole.ADMIN ? undefined : user?.id;
    return this.parkingLogsService.getStatistics(userId, startDate, endDate);
  }

  @Get('recent-activity')
  async getRecentActivity(
    @Query('limit', ParseIntPipe) limit?: number,
    @CurrentUser() user?: User,
  ) {
    const userId = user?.role === UserRole.ADMIN ? undefined : user?.id;
    return this.parkingLogsService.getRecentActivity(userId, limit);
  }

  @Get('slot/:slotId/history')
  async getSlotHistory(
    @Param('slotId', ParseIntPipe) slotId: number,
    @CurrentUser() user: User,
  ) {
    return this.parkingLogsService.findSlotHistory(slotId, user.id);
  }

  @Get('vehicle/:vehicleId/history')
  async getVehicleHistory(
    @Param('vehicleId', ParseIntPipe) vehicleId: number,
    @CurrentUser() user: User,
  ) {
    return this.parkingLogsService.findVehicleHistory(vehicleId, user.id);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ) {
    return this.parkingLogsService.findOne(id, user.id);
  }
}
