import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Check,
} from 'typeorm';

import { ParkingSlot } from '../../parking-slots/entities/parking-slot.entity';
import { Vehicle } from '../../vehicles/entities/vehicle.entity';
import { Guest } from '../../guests/entities/guest.entity';

export enum LogAction {
  ASSIGNED = 'Assigned',
  RELEASED = 'Released',
}

@Entity('parking_logs')
@Index(['slotId'])
@Index(['vehicleId'])
@Index(['guestId'])
@Index(['action'])
@Index(['assignedAt'])
@Check(`(vehicleId IS NOT NULL OR guestId IS NOT NULL)`)
export class ParkingLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  slotId: number | null;

  @Column({ nullable: true })
  vehicleId: number | null;

  @Column({ nullable: true })
  guestId: number | null;

  @Column({
    type: 'enum',
    enum: LogAction,
  })
  action: LogAction;

  @Column({ type: 'timestamp' })
  assignedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  releasedAt: Date;

  @Column('text', { nullable: true })
  notes: string;

  @CreateDateColumn()
  createdAt: Date;

  // Relationships
  @ManyToOne(() => ParkingSlot, (slot) => slot.parkingLogs, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'slotId' })
  slot: ParkingSlot;

  @ManyToOne(() => Vehicle, (vehicle) => vehicle.parkingLogs, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'vehicleId' })
  vehicle: Vehicle;

  @ManyToOne(() => Guest, (guest) => guest.parkingLogs, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'guestId' })
  guest: Guest;

  // Virtual properties
  get duration(): number | null {
    if (!this.releasedAt) {
      return null;
    }
    return this.releasedAt.getTime() - this.assignedAt.getTime();
  }

  get isActive(): boolean {
    return this.action === LogAction.ASSIGNED && !this.releasedAt;
  }

  get assignedTo(): string {
    if (this.vehicle) {
      return `Vehicle: ${this.vehicle.vehicleNumber}`;
    }
    if (this.guest) {
      return `Guest: ${this.guest.name} (${this.guest.vehicleNumber})`;
    }
    return 'Unknown';
  }
}
