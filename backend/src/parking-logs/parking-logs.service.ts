import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';

import { ParkingLog } from './entities/parking-log.entity';
import { User, UserRole } from '../users/entities/user.entity';
import { PaginationOptions, PaginatedResult } from '../users/users.service';

export interface LogFilterOptions extends PaginationOptions {
  startDate?: string;
  endDate?: string;
  plotId?: number;
  vehicleId?: number;
  guestId?: number;
  action?: string;
}

@Injectable()
export class ParkingLogsService {
  constructor(
    @InjectRepository(ParkingLog)
    private readonly parkingLogRepository: Repository<ParkingLog>,
  ) {}

  async findAll(options: LogFilterOptions = {}, user?: User): Promise<PaginatedResult<ParkingLog>> {
    const { 
      page = 1, 
      limit = 10, 
      search, 
      startDate, 
      endDate, 
      plotId, 
      vehicleId, 
      guestId, 
      action 
    } = options;
    const skip = (page - 1) * limit;

    const queryBuilder = this.parkingLogRepository.createQueryBuilder('log')
      .leftJoinAndSelect('log.slot', 'slot')
      .leftJoinAndSelect('slot.plot', 'plot')
      .leftJoinAndSelect('plot.user', 'plotUser')
      .leftJoinAndSelect('log.vehicle', 'vehicle')
      .leftJoinAndSelect('vehicle.user', 'vehicleUser')
      .leftJoinAndSelect('log.guest', 'guest')
      .leftJoinAndSelect('guest.user', 'guestUser');

    // If user is not admin, only show logs for their plots/vehicles/guests
    if (user && user.role !== UserRole.ADMIN) {
      queryBuilder.where(
        '(plot.userId = :userId OR vehicle.userId = :userId OR guest.userId = :userId)',
        { userId: user.id }
      );
    }

    // Apply filters
    if (startDate && endDate) {
      queryBuilder.andWhere('log.assignedAt BETWEEN :startDate AND :endDate', {
        startDate: new Date(startDate),
        endDate: new Date(endDate),
      });
    } else if (startDate) {
      queryBuilder.andWhere('log.assignedAt >= :startDate', {
        startDate: new Date(startDate),
      });
    } else if (endDate) {
      queryBuilder.andWhere('log.assignedAt <= :endDate', {
        endDate: new Date(endDate),
      });
    }

    if (plotId) {
      queryBuilder.andWhere('slot.plotId = :plotId', { plotId });
    }

    if (vehicleId) {
      queryBuilder.andWhere('log.vehicleId = :vehicleId', { vehicleId });
    }

    if (guestId) {
      queryBuilder.andWhere('log.guestId = :guestId', { guestId });
    }

    if (action) {
      queryBuilder.andWhere('log.action = :action', { action });
    }

    if (search) {
      queryBuilder.andWhere(
        '(vehicle.vehicleNumber LIKE :search OR guest.name LIKE :search OR guest.vehicleNumber LIKE :search OR slot.slotNumber LIKE :search OR plot.name LIKE :search)',
        { search: `%${search}%` }
      );
    }

    const [data, total] = await queryBuilder
      .skip(skip)
      .take(limit)
      .orderBy('log.assignedAt', 'DESC')
      .getManyAndCount();

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: number, userId?: number): Promise<ParkingLog> {
    const queryBuilder = this.parkingLogRepository.createQueryBuilder('log')
      .leftJoinAndSelect('log.slot', 'slot')
      .leftJoinAndSelect('slot.plot', 'plot')
      .leftJoinAndSelect('plot.user', 'plotUser')
      .leftJoinAndSelect('log.vehicle', 'vehicle')
      .leftJoinAndSelect('vehicle.user', 'vehicleUser')
      .leftJoinAndSelect('log.guest', 'guest')
      .leftJoinAndSelect('guest.user', 'guestUser')
      .where('log.id = :id', { id });

    // If userId is provided and user is not admin, check ownership
    if (userId) {
      const user = await this.parkingLogRepository.manager.findOne(User, { where: { id: userId } });
      if (user && user.role !== UserRole.ADMIN) {
        queryBuilder.andWhere(
          '(plot.userId = :userId OR vehicle.userId = :userId OR guest.userId = :userId)',
          { userId }
        );
      }
    }

    const log = await queryBuilder.getOne();

    if (!log) {
      throw new NotFoundException('Parking log not found');
    }

    return log;
  }

  async findUserLogs(userId: number, options: LogFilterOptions = {}): Promise<PaginatedResult<ParkingLog>> {
    const user = await this.parkingLogRepository.manager.findOne(User, { where: { id: userId } });
    return this.findAll(options, user);
  }

  async findSlotHistory(slotId: number, userId?: number): Promise<ParkingLog[]> {
    const queryBuilder = this.parkingLogRepository.createQueryBuilder('log')
      .leftJoinAndSelect('log.slot', 'slot')
      .leftJoinAndSelect('slot.plot', 'plot')
      .leftJoinAndSelect('log.vehicle', 'vehicle')
      .leftJoinAndSelect('log.guest', 'guest')
      .where('log.slotId = :slotId', { slotId });

    // If userId is provided and user is not admin, check ownership
    if (userId) {
      const user = await this.parkingLogRepository.manager.findOne(User, { where: { id: userId } });
      if (user && user.role !== UserRole.ADMIN) {
        queryBuilder.andWhere('plot.userId = :userId', { userId });
      }
    }

    return queryBuilder
      .orderBy('log.assignedAt', 'DESC')
      .getMany();
  }

  async findVehicleHistory(vehicleId: number, userId?: number): Promise<ParkingLog[]> {
    const queryBuilder = this.parkingLogRepository.createQueryBuilder('log')
      .leftJoinAndSelect('log.slot', 'slot')
      .leftJoinAndSelect('slot.plot', 'plot')
      .leftJoinAndSelect('log.vehicle', 'vehicle')
      .where('log.vehicleId = :vehicleId', { vehicleId });

    // If userId is provided and user is not admin, check ownership
    if (userId) {
      const user = await this.parkingLogRepository.manager.findOne(User, { where: { id: userId } });
      if (user && user.role !== UserRole.ADMIN) {
        queryBuilder.andWhere('vehicle.userId = :userId', { userId });
      }
    }

    return queryBuilder
      .orderBy('log.assignedAt', 'DESC')
      .getMany();
  }

  async getStatistics(userId?: number, startDate?: string, endDate?: string): Promise<any> {
    const queryBuilder = this.parkingLogRepository.createQueryBuilder('log')
      .leftJoin('log.slot', 'slot')
      .leftJoin('slot.plot', 'plot')
      .leftJoin('log.vehicle', 'vehicle')
      .leftJoin('log.guest', 'guest');

    // If userId is provided and user is not admin, filter by user's data
    if (userId) {
      const user = await this.parkingLogRepository.manager.findOne(User, { where: { id: userId } });
      if (user && user.role !== UserRole.ADMIN) {
        queryBuilder.where(
          '(plot.userId = :userId OR vehicle.userId = :userId OR guest.userId = :userId)',
          { userId }
        );
      }
    }

    // Apply date filters
    if (startDate && endDate) {
      queryBuilder.andWhere('log.assignedAt BETWEEN :startDate AND :endDate', {
        startDate: new Date(startDate),
        endDate: new Date(endDate),
      });
    }

    const totalLogs = await queryBuilder.getCount();

    const assignmentLogs = await queryBuilder
      .clone()
      .andWhere('log.action = :action', { action: 'Assigned' })
      .getCount();

    const releaseLogs = await queryBuilder
      .clone()
      .andWhere('log.action = :action', { action: 'Released' })
      .getCount();

    const vehicleLogs = await queryBuilder
      .clone()
      .andWhere('log.vehicleId IS NOT NULL')
      .getCount();

    const guestLogs = await queryBuilder
      .clone()
      .andWhere('log.guestId IS NOT NULL')
      .getCount();

    // Calculate average parking duration
    const completedSessions = await this.parkingLogRepository
      .createQueryBuilder('log')
      .select('AVG(TIMESTAMPDIFF(MINUTE, log.assignedAt, log.releasedAt))', 'avgDuration')
      .where('log.releasedAt IS NOT NULL')
      .getRawOne();

    return {
      totalLogs,
      assignmentLogs,
      releaseLogs,
      vehicleLogs,
      guestLogs,
      averageParkingDurationMinutes: completedSessions?.avgDuration || 0,
    };
  }

  async getRecentActivity(userId?: number, limit: number = 10): Promise<ParkingLog[]> {
    const queryBuilder = this.parkingLogRepository.createQueryBuilder('log')
      .leftJoinAndSelect('log.slot', 'slot')
      .leftJoinAndSelect('slot.plot', 'plot')
      .leftJoinAndSelect('log.vehicle', 'vehicle')
      .leftJoinAndSelect('log.guest', 'guest');

    // If userId is provided and user is not admin, filter by user's data
    if (userId) {
      const user = await this.parkingLogRepository.manager.findOne(User, { where: { id: userId } });
      if (user && user.role !== UserRole.ADMIN) {
        queryBuilder.where(
          '(plot.userId = :userId OR vehicle.userId = :userId OR guest.userId = :userId)',
          { userId }
        );
      }
    }

    return queryBuilder
      .orderBy('log.assignedAt', 'DESC')
      .limit(limit)
      .getMany();
  }
}
