import { IsNotEmpty, <PERSON>String, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Min, IsOptional } from 'class-validator';
import { PlotType } from '../entities/plot.entity';

export class CreatePlotDto {
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name is required' })
  name: string;

  @IsEnum(PlotType, { message: 'Type must be one of: Mall, Apartment, Office, Residential' })
  @IsNotEmpty({ message: 'Type is required' })
  type: PlotType;

  @IsString({ message: 'Location must be a string' })
  @IsNotEmpty({ message: 'Location is required' })
  location: string;

  @IsInt({ message: 'Total slots must be an integer' })
  @Min(1, { message: 'Total slots must be at least 1' })
  totalSlots: number;

  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  description?: string;
}
