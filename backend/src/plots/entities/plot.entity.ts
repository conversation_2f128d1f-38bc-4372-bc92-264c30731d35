import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';

import { User } from '../../users/entities/user.entity';
import { ParkingSlot } from '../../parking-slots/entities/parking-slot.entity';

export enum PlotType {
  MALL = 'Mall',
  APARTMENT = 'Apartment',
  OFFICE = 'Office',
  RESIDENTIAL = 'Residential',
}

@Entity('plots')
@Index(['userId'])
@Index(['type'])
@Index(['isActive'])
export class Plot {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  userId: number;

  @Column({ length: 255 })
  name: string;

  @Column({
    type: 'enum',
    enum: PlotType,
  })
  type: PlotType;

  @Column('text')
  location: string;

  @Column({ type: 'int', unsigned: true })
  totalSlots: number;

  @Column('text', { nullable: true })
  description: string;

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => User, (user) => user.plots, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @OneToMany(() => ParkingSlot, (slot) => slot.plot)
  parkingSlots: ParkingSlot[];

  // Virtual properties
  get availableSlots(): number {
    if (!this.parkingSlots) return 0;
    return this.parkingSlots.filter(slot => slot.status === 'Available').length;
  }

  get occupiedSlots(): number {
    if (!this.parkingSlots) return 0;
    return this.parkingSlots.filter(slot => slot.status === 'Occupied').length;
  }

  get maintenanceSlots(): number {
    if (!this.parkingSlots) return 0;
    return this.parkingSlots.filter(slot => slot.status === 'Maintenance').length;
  }

  get occupancyRate(): number {
    if (!this.parkingSlots || this.parkingSlots.length === 0) return 0;
    return (this.occupiedSlots / this.parkingSlots.length) * 100;
  }
}
