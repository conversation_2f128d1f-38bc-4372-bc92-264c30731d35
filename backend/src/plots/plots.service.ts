import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Plot } from './entities/plot.entity';
import { ParkingSlot, SlotStatus } from '../parking-slots/entities/parking-slot.entity';
import { CreatePlotDto } from './dto/create-plot.dto';
import { UpdatePlotDto } from './dto/update-plot.dto';
import { User, UserRole } from '../users/entities/user.entity';
import { PaginationOptions, PaginatedResult } from '../users/users.service';

@Injectable()
export class PlotsService {
  constructor(
    @InjectRepository(Plot)
    private readonly plotRepository: Repository<Plot>,
    @InjectRepository(ParkingSlot)
    private readonly parkingSlotRepository: Repository<ParkingSlot>,
  ) {}

  async create(createPlotDto: CreatePlotDto, userId: number): Promise<Plot> {
    const plot = this.plotRepository.create({
      ...createPlotDto,
      userId,
    });

    const savedPlot = await this.plotRepository.save(plot);

    // Create parking slots for the plot
    await this.createParkingSlotsForPlot(savedPlot.id, createPlotDto.totalSlots);

    return this.findOne(savedPlot.id, userId);
  }

  async findAll(options: PaginationOptions = {}, user?: User): Promise<PaginatedResult<Plot>> {
    const { page = 1, limit = 10, search } = options;
    const skip = (page - 1) * limit;

    const queryBuilder = this.plotRepository.createQueryBuilder('plot')
      .leftJoinAndSelect('plot.user', 'user')
      .leftJoinAndSelect('plot.parkingSlots', 'slots')
      .select([
        'plot.id',
        'plot.name',
        'plot.type',
        'plot.location',
        'plot.totalSlots',
        'plot.description',
        'plot.isActive',
        'plot.createdAt',
        'plot.updatedAt',
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.email',
        'slots.id',
        'slots.status',
      ]);

    // If user is not admin, only show their plots
    if (user && user.role !== UserRole.ADMIN) {
      queryBuilder.where('plot.userId = :userId', { userId: user.id });
    }

    if (search) {
      queryBuilder.andWhere(
        '(plot.name LIKE :search OR plot.location LIKE :search OR plot.type LIKE :search)',
        { search: `%${search}%` }
      );
    }

    const [data, total] = await queryBuilder
      .skip(skip)
      .take(limit)
      .orderBy('plot.createdAt', 'DESC')
      .getManyAndCount();

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: number, userId?: number): Promise<Plot> {
    const queryBuilder = this.plotRepository.createQueryBuilder('plot')
      .leftJoinAndSelect('plot.user', 'user')
      .leftJoinAndSelect('plot.parkingSlots', 'slots')
      .leftJoinAndSelect('slots.vehicle', 'vehicle')
      .where('plot.id = :id', { id });

    // If userId is provided and user is not admin, check ownership
    if (userId) {
      const user = await this.plotRepository.manager.findOne(User, { where: { id: userId } });
      if (user && user.role !== UserRole.ADMIN) {
        queryBuilder.andWhere('plot.userId = :userId', { userId });
      }
    }

    const plot = await queryBuilder.getOne();

    if (!plot) {
      throw new NotFoundException('Plot not found');
    }

    return plot;
  }

  async update(id: number, updatePlotDto: UpdatePlotDto, userId: number): Promise<Plot> {
    const plot = await this.findOne(id, userId);

    // Check ownership (unless admin)
    const user = await this.plotRepository.manager.findOne(User, { where: { id: userId } });
    if (user?.role !== UserRole.ADMIN && plot.userId !== userId) {
      throw new ForbiddenException('You can only update your own plots');
    }

    Object.assign(plot, updatePlotDto);
    await this.plotRepository.save(plot);

    return this.findOne(id, userId);
  }

  async remove(id: number, userId: number): Promise<void> {
    const plot = await this.findOne(id, userId);

    // Check ownership (unless admin)
    const user = await this.plotRepository.manager.findOne(User, { where: { id: userId } });
    if (user?.role !== UserRole.ADMIN && plot.userId !== userId) {
      throw new ForbiddenException('You can only delete your own plots');
    }

    // Check if plot has occupied slots
    const occupiedSlots = await this.parkingSlotRepository.count({
      where: { plotId: id, status: SlotStatus.OCCUPIED },
    });

    if (occupiedSlots > 0) {
      throw new BadRequestException('Cannot delete plot with occupied parking slots');
    }

    await this.plotRepository.remove(plot);
  }

  async getPlotStats(plotId: number, userId?: number): Promise<any> {
    const plot = await this.findOne(plotId, userId);

    const stats = {
      totalSlots: plot.totalSlots,
      availableSlots: plot.availableSlots,
      occupiedSlots: plot.occupiedSlots,
      maintenanceSlots: plot.maintenanceSlots,
      occupancyRate: plot.occupancyRate,
    };

    return stats;
  }

  private async createParkingSlotsForPlot(plotId: number, totalSlots: number): Promise<void> {
    const slots = [];
    for (let i = 1; i <= totalSlots; i++) {
      slots.push(
        this.parkingSlotRepository.create({
          plotId: plotId,
          slotNumber: i.toString().padStart(3, '0'),
          status: SlotStatus.AVAILABLE,
        })
      );
    }

    await this.parkingSlotRepository.save(slots);
  }

  async findUserPlots(userId: number): Promise<Plot[]> {
    return this.plotRepository.find({
      where: { userId, isActive: true },
      relations: ['parkingSlots'],
      order: { createdAt: 'DESC' },
    });
  }
}
