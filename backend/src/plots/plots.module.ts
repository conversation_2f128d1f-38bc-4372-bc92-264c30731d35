import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { PlotsService } from './plots.service';
import { PlotsController } from './plots.controller';
import { Plot } from './entities/plot.entity';
import { ParkingSlot } from '../parking-slots/entities/parking-slot.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Plot, ParkingSlot])],
  controllers: [PlotsController],
  providers: [PlotsService],
  exports: [PlotsService],
})
export class PlotsModule {}
