import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  ParseIntPipe,
  ValidationPipe,
} from '@nestjs/common';

import { PlotsService } from './plots.service';
import { CreatePlotDto } from './dto/create-plot.dto';
import { UpdatePlotDto } from './dto/update-plot.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User, UserRole } from '../users/entities/user.entity';
import { PaginationOptions } from '../users/users.service';

@Controller('plots')
@UseGuards(JwtAuthGuard, RolesGuard)
export class PlotsController {
  constructor(private readonly plotsService: PlotsService) {}

  @Post()
  @Roles(UserRole.USER, UserRole.ADMIN)
  async create(
    @Body(ValidationPipe) createPlotDto: CreatePlotDto,
    @CurrentUser() user: User,
  ) {
    return this.plotsService.create(createPlotDto, user.id);
  }

  @Get()
  async findAll(
    @Query('page', ParseIntPipe) page?: number,
    @Query('limit', ParseIntPipe) limit?: number,
    @Query('search') search?: string,
    @CurrentUser() user?: User,
  ) {
    const options: PaginationOptions = { page, limit, search };
    return this.plotsService.findAll(options, user);
  }

  @Get('my-plots')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async findMyPlots(@CurrentUser() user: User) {
    return this.plotsService.findUserPlots(user.id);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ) {
    return this.plotsService.findOne(id, user.id);
  }

  @Get(':id/stats')
  async getPlotStats(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ) {
    return this.plotsService.getPlotStats(id, user.id);
  }

  @Patch(':id')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body(ValidationPipe) updatePlotDto: UpdatePlotDto,
    @CurrentUser() user: User,
  ) {
    return this.plotsService.update(id, updatePlotDto, user.id);
  }

  @Delete(':id')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ) {
    await this.plotsService.remove(id, user.id);
    return { message: 'Plot deleted successfully' };
  }
}
