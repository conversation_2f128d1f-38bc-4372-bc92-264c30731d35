import { IsNotEmpty, IsString, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsInt } from 'class-validator';
import { SlotStatus } from '../entities/parking-slot.entity';

export class CreateParkingSlotDto {
  @IsInt({ message: 'Plot ID must be an integer' })
  @IsNotEmpty({ message: 'Plot ID is required' })
  plotId: number;

  @IsString({ message: 'Slot number must be a string' })
  @IsNotEmpty({ message: 'Slot number is required' })
  slotNumber: string;

  @IsOptional()
  @IsEnum(SlotStatus, { message: 'Status must be one of: Available, Occupied, Maintenance' })
  status?: SlotStatus;
}
