import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ParkingSlotsService } from './parking-slots.service';
import { ParkingSlotsController } from './parking-slots.controller';
import { ParkingSlot } from './entities/parking-slot.entity';
import { ParkingLog } from '../parking-logs/entities/parking-log.entity';
import { Vehicle } from '../vehicles/entities/vehicle.entity';
import { Guest } from '../guests/entities/guest.entity';
import { Plot } from '../plots/entities/plot.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ParkingSlot, ParkingLog, Vehicle, Guest, Plot])],
  controllers: [ParkingSlotsController],
  providers: [ParkingSlotsService],
  exports: [ParkingSlotsService],
})
export class ParkingSlotsModule {}
