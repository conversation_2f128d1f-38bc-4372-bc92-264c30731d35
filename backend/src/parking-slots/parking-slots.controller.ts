import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  ParseIntPipe,
  ValidationPipe,
} from '@nestjs/common';

import { ParkingSlotsService } from './parking-slots.service';
import { CreateParkingSlotDto } from './dto/create-parking-slot.dto';
import { UpdateParkingSlotDto } from './dto/update-parking-slot.dto';
import { AssignSlotDto } from './dto/assign-slot.dto';
import { ReleaseSlotDto } from './dto/release-slot.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User, UserRole } from '../users/entities/user.entity';

@Controller('parking-slots')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ParkingSlotsController {
  constructor(private readonly parkingSlotsService: ParkingSlotsService) {}

  @Post()
  @Roles(UserRole.USER, UserRole.ADMIN)
  async create(
    @Body(ValidationPipe) createParkingSlotDto: CreateParkingSlotDto,
    @CurrentUser() user: User,
  ) {
    return this.parkingSlotsService.create(createParkingSlotDto, user.id);
  }

  @Get()
  async findAll(
    @Query('plotId', ParseIntPipe) plotId?: number,
    @CurrentUser() user?: User,
  ) {
    return this.parkingSlotsService.findAll(plotId, user?.id);
  }

  @Get('available')
  async findAvailable(
    @Query('plotId', ParseIntPipe) plotId: number,
    @CurrentUser() user: User,
  ) {
    return this.parkingSlotsService.findAvailableSlots(plotId, user.id);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ) {
    return this.parkingSlotsService.findOne(id, user.id);
  }

  @Patch(':id')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body(ValidationPipe) updateParkingSlotDto: UpdateParkingSlotDto,
    @CurrentUser() user: User,
  ) {
    return this.parkingSlotsService.update(id, updateParkingSlotDto, user.id);
  }

  @Post(':id/assign')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async assignSlot(
    @Param('id', ParseIntPipe) id: number,
    @Body(ValidationPipe) assignSlotDto: AssignSlotDto,
    @CurrentUser() user: User,
  ) {
    return this.parkingSlotsService.assignSlot(id, assignSlotDto, user.id);
  }

  @Post(':id/release')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async releaseSlot(
    @Param('id', ParseIntPipe) id: number,
    @Body(ValidationPipe) releaseSlotDto: ReleaseSlotDto,
    @CurrentUser() user: User,
  ) {
    return this.parkingSlotsService.releaseSlot(id, releaseSlotDto, user.id);
  }

  @Delete(':id')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ) {
    await this.parkingSlotsService.remove(id, user.id);
    return { message: 'Parking slot deleted successfully' };
  }
}
