import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';

import { Plot } from '../../plots/entities/plot.entity';
import { Vehicle } from '../../vehicles/entities/vehicle.entity';
import { ParkingLog } from '../../parking-logs/entities/parking-log.entity';

export enum SlotStatus {
  AVAILABLE = 'Available',
  OCCUPIED = 'Occupied',
  MAINTENANCE = 'Maintenance',
}

@Entity('parking_slots')
@Unique(['plotId', 'slotNumber'])
@Index(['plotId'])
@Index(['status'])
@Index(['vehicleId'])
export class ParkingSlot {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  plotId: number;

  @Column({ length: 50 })
  slotNumber: string;

  @Column({
    type: 'enum',
    enum: SlotStatus,
    default: SlotStatus.AVAILABLE,
  })
  status: SlotStatus;

  @Column({ nullable: true })
  vehicleId: number;

  @Column({ type: 'timestamp', nullable: true })
  allocatedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => Plot, (plot) => plot.parkingSlots, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'plotId' })
  plot: Plot;

  @ManyToOne(() => Vehicle, (vehicle) => vehicle.parkingSlots, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'vehicleId' })
  vehicle: Vehicle;

  @OneToMany(() => ParkingLog, (log) => log.slot)
  parkingLogs: ParkingLog[];

  // Virtual properties
  get isAvailable(): boolean {
    return this.status === SlotStatus.AVAILABLE;
  }

  get isOccupied(): boolean {
    return this.status === SlotStatus.OCCUPIED;
  }

  get isUnderMaintenance(): boolean {
    return this.status === SlotStatus.MAINTENANCE;
  }

  get displayName(): string {
    return `${this.plot?.name || 'Unknown Plot'} - ${this.slotNumber}`;
  }
}
