import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { ParkingSlot, SlotStatus } from './entities/parking-slot.entity';
import { ParkingLog, LogAction } from '../parking-logs/entities/parking-log.entity';
import { Vehicle } from '../vehicles/entities/vehicle.entity';
import { Guest } from '../guests/entities/guest.entity';
import { Plot } from '../plots/entities/plot.entity';
import { User, UserRole } from '../users/entities/user.entity';
import { CreateParkingSlotDto } from './dto/create-parking-slot.dto';
import { UpdateParkingSlotDto } from './dto/update-parking-slot.dto';
import { AssignSlotDto } from './dto/assign-slot.dto';
import { ReleaseSlotDto } from './dto/release-slot.dto';

@Injectable()
export class ParkingSlotsService {
  constructor(
    @InjectRepository(ParkingSlot)
    private readonly parkingSlotRepository: Repository<ParkingSlot>,
    @InjectRepository(ParkingLog)
    private readonly parkingLogRepository: Repository<ParkingLog>,
    @InjectRepository(Vehicle)
    private readonly vehicleRepository: Repository<Vehicle>,
    @InjectRepository(Guest)
    private readonly guestRepository: Repository<Guest>,
    @InjectRepository(Plot)
    private readonly plotRepository: Repository<Plot>,
  ) {}

  async create(createParkingSlotDto: CreateParkingSlotDto, userId: number): Promise<ParkingSlot> {
    // Verify plot ownership
    const plot = await this.plotRepository.findOne({
      where: { id: createParkingSlotDto.plotId },
    });

    if (!plot) {
      throw new NotFoundException('Plot not found');
    }

    const user = await this.parkingSlotRepository.manager.findOne(User, { where: { id: userId } });
    if (user?.role !== UserRole.ADMIN && plot.userId !== userId) {
      throw new ForbiddenException('You can only create slots in your own plots');
    }

    // Check if slot number already exists in the plot
    const existingSlot = await this.parkingSlotRepository.findOne({
      where: { 
        plotId: createParkingSlotDto.plotId, 
        slotNumber: createParkingSlotDto.slotNumber 
      },
    });

    if (existingSlot) {
      throw new BadRequestException('Slot number already exists in this plot');
    }

    const slot = this.parkingSlotRepository.create(createParkingSlotDto);
    return this.parkingSlotRepository.save(slot);
  }

  async findAll(plotId?: number, userId?: number): Promise<ParkingSlot[]> {
    const queryBuilder = this.parkingSlotRepository.createQueryBuilder('slot')
      .leftJoinAndSelect('slot.plot', 'plot')
      .leftJoinAndSelect('slot.vehicle', 'vehicle')
      .leftJoinAndSelect('plot.user', 'user');

    if (plotId) {
      queryBuilder.where('slot.plotId = :plotId', { plotId });
    }

    // If userId is provided and user is not admin, filter by user's plots
    if (userId) {
      const user = await this.parkingSlotRepository.manager.findOne(User, { where: { id: userId } });
      if (user && user.role !== UserRole.ADMIN) {
        queryBuilder.andWhere('plot.userId = :userId', { userId });
      }
    }

    return queryBuilder
      .orderBy('plot.name', 'ASC')
      .addOrderBy('slot.slotNumber', 'ASC')
      .getMany();
  }

  async findOne(id: number, userId?: number): Promise<ParkingSlot> {
    const queryBuilder = this.parkingSlotRepository.createQueryBuilder('slot')
      .leftJoinAndSelect('slot.plot', 'plot')
      .leftJoinAndSelect('slot.vehicle', 'vehicle')
      .leftJoinAndSelect('slot.parkingLogs', 'logs')
      .leftJoinAndSelect('logs.vehicle', 'logVehicle')
      .leftJoinAndSelect('logs.guest', 'logGuest')
      .where('slot.id = :id', { id });

    // If userId is provided and user is not admin, check ownership
    if (userId) {
      const user = await this.parkingSlotRepository.manager.findOne(User, { where: { id: userId } });
      if (user && user.role !== UserRole.ADMIN) {
        queryBuilder.andWhere('plot.userId = :userId', { userId });
      }
    }

    const slot = await queryBuilder.getOne();

    if (!slot) {
      throw new NotFoundException('Parking slot not found');
    }

    return slot;
  }

  async update(id: number, updateParkingSlotDto: UpdateParkingSlotDto, userId: number): Promise<ParkingSlot> {
    const slot = await this.findOne(id, userId);

    // Check ownership (unless admin)
    const user = await this.parkingSlotRepository.manager.findOne(User, { where: { id: userId } });
    if (user?.role !== UserRole.ADMIN && slot.plot.userId !== userId) {
      throw new ForbiddenException('You can only update slots in your own plots');
    }

    Object.assign(slot, updateParkingSlotDto);
    return this.parkingSlotRepository.save(slot);
  }

  async remove(id: number, userId: number): Promise<void> {
    const slot = await this.findOne(id, userId);

    // Check ownership (unless admin)
    const user = await this.parkingSlotRepository.manager.findOne(User, { where: { id: userId } });
    if (user?.role !== UserRole.ADMIN && slot.plot.userId !== userId) {
      throw new ForbiddenException('You can only delete slots in your own plots');
    }

    // Check if slot is occupied
    if (slot.status === SlotStatus.OCCUPIED) {
      throw new BadRequestException('Cannot delete occupied parking slot');
    }

    await this.parkingSlotRepository.remove(slot);
  }

  async assignSlot(id: number, assignSlotDto: AssignSlotDto, userId: number): Promise<ParkingSlot> {
    const slot = await this.findOne(id, userId);

    // Check ownership (unless admin)
    const user = await this.parkingSlotRepository.manager.findOne(User, { where: { id: userId } });
    if (user?.role !== UserRole.ADMIN && slot.plot.userId !== userId) {
      throw new ForbiddenException('You can only assign slots in your own plots');
    }

    // Check if slot is available
    if (slot.status !== SlotStatus.AVAILABLE) {
      throw new BadRequestException('Slot is not available for assignment');
    }

    // Validate assignment data
    if (!assignSlotDto.vehicleId && !assignSlotDto.guestId) {
      throw new BadRequestException('Either vehicleId or guestId must be provided');
    }

    if (assignSlotDto.vehicleId && assignSlotDto.guestId) {
      throw new BadRequestException('Cannot assign both vehicle and guest to the same slot');
    }

    let vehicle: Vehicle | null = null;
    let guest: Guest | null = null;

    if (assignSlotDto.vehicleId) {
      vehicle = await this.vehicleRepository.findOne({
        where: { id: assignSlotDto.vehicleId, userId },
      });

      if (!vehicle) {
        throw new NotFoundException('Vehicle not found or not owned by user');
      }

      // Check if vehicle is already parked
      const existingAssignment = await this.parkingSlotRepository.findOne({
        where: { vehicleId: vehicle.id, status: SlotStatus.OCCUPIED },
      });

      if (existingAssignment) {
        throw new BadRequestException('Vehicle is already parked in another slot');
      }
    }

    if (assignSlotDto.guestId) {
      guest = await this.guestRepository.findOne({
        where: { id: assignSlotDto.guestId, userId },
      });

      if (!guest) {
        throw new NotFoundException('Guest not found or not owned by user');
      }
    }

    // Update slot
    slot.status = SlotStatus.OCCUPIED;
    slot.vehicleId = assignSlotDto.vehicleId || null;
    slot.allocatedAt = new Date();

    await this.parkingSlotRepository.save(slot);

    // Create parking log
    const log = this.parkingLogRepository.create({
      slotId: slot.id,
      vehicleId: assignSlotDto.vehicleId || null,
      guestId: assignSlotDto.guestId || null,
      action: LogAction.ASSIGNED,
      assignedAt: new Date(),
      notes: assignSlotDto.notes,
    });

    await this.parkingLogRepository.save(log);

    return this.findOne(id, userId);
  }

  async releaseSlot(id: number, releaseSlotDto: ReleaseSlotDto, userId: number): Promise<ParkingSlot> {
    const slot = await this.findOne(id, userId);

    // Check ownership (unless admin)
    const user = await this.parkingSlotRepository.manager.findOne(User, { where: { id: userId } });
    if (user?.role !== UserRole.ADMIN && slot.plot.userId !== userId) {
      throw new ForbiddenException('You can only release slots in your own plots');
    }

    // Check if slot is occupied
    if (slot.status !== SlotStatus.OCCUPIED) {
      throw new BadRequestException('Slot is not occupied');
    }

    // Find the active parking log
    const activeLog = await this.parkingLogRepository.findOne({
      where: {
        slotId: slot.id,
        action: LogAction.ASSIGNED
      },
    });

    if (activeLog) {
      activeLog.releasedAt = new Date();
      activeLog.notes = releaseSlotDto.notes || '';
      await this.parkingLogRepository.save(activeLog);
    }

    // Create release log
    const releaseLog = this.parkingLogRepository.create({
      slotId: slot.id,
      vehicleId: slot.vehicleId,
      guestId: null, // Will be set if it was a guest assignment
      action: LogAction.RELEASED,
      assignedAt: new Date(),
      notes: releaseSlotDto.notes,
    });

    await this.parkingLogRepository.save(releaseLog);

    // Update slot
    slot.status = SlotStatus.AVAILABLE;
    slot.vehicleId = null;
    slot.allocatedAt = null;

    await this.parkingSlotRepository.save(slot);

    return this.findOne(id, userId);
  }

  async findAvailableSlots(plotId: number, userId?: number): Promise<ParkingSlot[]> {
    const queryBuilder = this.parkingSlotRepository.createQueryBuilder('slot')
      .leftJoinAndSelect('slot.plot', 'plot')
      .where('slot.plotId = :plotId', { plotId })
      .andWhere('slot.status = :status', { status: SlotStatus.AVAILABLE });

    // If userId is provided and user is not admin, check ownership
    if (userId) {
      const user = await this.parkingSlotRepository.manager.findOne(User, { where: { id: userId } });
      if (user && user.role !== UserRole.ADMIN) {
        queryBuilder.andWhere('plot.userId = :userId', { userId });
      }
    }

    return queryBuilder
      .orderBy('slot.slotNumber', 'ASC')
      .getMany();
  }
}
