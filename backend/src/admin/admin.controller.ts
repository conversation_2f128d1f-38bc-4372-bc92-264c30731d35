import {
  Controller,
  Get,
  UseGuards,
  Query,
  ParseIntPipe,
} from '@nestjs/common';

import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';
import { UsersService, PaginationOptions } from '../users/users.service';
import { PlotsService } from '../plots/plots.service';
import { ParkingLogsService } from '../parking-logs/parking-logs.service';

@Controller('admin')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
export class AdminController {
  constructor(
    private readonly usersService: UsersService,
    private readonly plotsService: PlotsService,
    private readonly parkingLogsService: ParkingLogsService,
  ) {}

  @Get('dashboard/stats')
  async getDashboardStats() {
    // Get total users
    const usersResult = await this.usersService.findAll({ page: 1, limit: 1 });
    const totalUsers = usersResult.total;

    // Get total plots
    const plotsResult = await this.plotsService.findAll({ page: 1, limit: 1 });
    const totalPlots = plotsResult.total;

    // Calculate total slots and occupancy
    let totalSlots = 0;
    let occupiedSlots = 0;

    for (const plot of plotsResult.data) {
      totalSlots += plot.totalSlots;
      occupiedSlots += plot.occupiedSlots;
    }

    const occupancyRate = totalSlots > 0 ? (occupiedSlots / totalSlots) * 100 : 0;

    // Get recent activity
    const recentActivity = await this.parkingLogsService.getRecentActivity(undefined, 5);

    // Get statistics for the current month
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    const monthlyStats = await this.parkingLogsService.getStatistics(
      undefined,
      startOfMonth.toISOString(),
      endOfMonth.toISOString()
    );

    return {
      totalUsers,
      totalPlots,
      totalSlots,
      occupiedSlots,
      availableSlots: totalSlots - occupiedSlots,
      occupancyRate: Math.round(occupancyRate * 100) / 100,
      recentActivity,
      monthlyStats,
    };
  }

  @Get('users')
  async getUsers(
    @Query('page', ParseIntPipe) page?: number,
    @Query('limit', ParseIntPipe) limit?: number,
    @Query('search') search?: string,
  ) {
    const options: PaginationOptions = { page, limit, search };
    return this.usersService.findAll(options);
  }

  @Get('users/:id/plots')
  async getUserPlots(@Query('id', ParseIntPipe) userId: number) {
    return this.plotsService.findUserPlots(userId);
  }

  @Get('system-overview')
  async getSystemOverview() {
    // Get all plots with their slot information
    const allPlots = await this.plotsService.findAll({ page: 1, limit: 1000 });
    
    const plotsByType = allPlots.data.reduce((acc, plot) => {
      if (!acc[plot.type]) {
        acc[plot.type] = {
          count: 0,
          totalSlots: 0,
          occupiedSlots: 0,
        };
      }
      acc[plot.type].count++;
      acc[plot.type].totalSlots += plot.totalSlots;
      acc[plot.type].occupiedSlots += plot.occupiedSlots;
      return acc;
    }, {});

    // Get user statistics
    const usersResult = await this.usersService.findAll({ page: 1, limit: 1000 });
    const activeUsers = usersResult.data.filter(user => user.isActive).length;
    const inactiveUsers = usersResult.data.filter(user => !user.isActive).length;

    return {
      plotsByType,
      userStats: {
        total: usersResult.total,
        active: activeUsers,
        inactive: inactiveUsers,
      },
    };
  }

  @Get('reports/occupancy')
  async getOccupancyReport(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    // This would typically involve more complex queries
    // For now, return basic statistics
    return this.parkingLogsService.getStatistics(undefined, startDate, endDate);
  }
}
