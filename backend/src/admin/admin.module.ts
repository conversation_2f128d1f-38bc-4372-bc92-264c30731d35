import { Module } from '@nestjs/common';

import { AdminController } from './admin.controller';
import { UsersModule } from '../users/users.module';
import { PlotsModule } from '../plots/plots.module';
import { ParkingLogsModule } from '../parking-logs/parking-logs.module';

@Module({
  imports: [UsersModule, PlotsModule, ParkingLogsModule],
  controllers: [AdminController],
})
export class AdminModule {}
