import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Guest, GuestStatus } from './entities/guest.entity';
import { CreateGuestDto } from './dto/create-guest.dto';
import { UpdateGuestDto } from './dto/update-guest.dto';
import { User, UserRole } from '../users/entities/user.entity';
import { PaginationOptions, PaginatedResult } from '../users/users.service';

@Injectable()
export class GuestsService {
  constructor(
    @InjectRepository(Guest)
    private readonly guestRepository: Repository<Guest>,
  ) {}

  async create(createGuestDto: CreateGuestDto, userId: number): Promise<Guest> {
    const guest = this.guestRepository.create({
      ...createGuestDto,
      userId,
      expectedVisitTime: new Date(createGuestDto.expectedVisitTime),
    });

    return this.guestRepository.save(guest);
  }

  async findAll(options: PaginationOptions = {}, user?: User): Promise<PaginatedResult<Guest>> {
    const { page = 1, limit = 10, search } = options;
    const skip = (page - 1) * limit;

    const queryBuilder = this.guestRepository.createQueryBuilder('guest')
      .leftJoinAndSelect('guest.user', 'user')
      .leftJoinAndSelect('guest.parkingLogs', 'logs')
      .leftJoinAndSelect('logs.slot', 'slot')
      .leftJoinAndSelect('slot.plot', 'plot')
      .select([
        'guest.id',
        'guest.name',
        'guest.contact',
        'guest.vehicleNumber',
        'guest.purpose',
        'guest.expectedVisitTime',
        'guest.actualArrivalTime',
        'guest.actualDepartureTime',
        'guest.status',
        'guest.createdAt',
        'guest.updatedAt',
        'user.id',
        'user.firstName',
        'user.lastName',
        'logs.id',
        'logs.action',
        'logs.assignedAt',
        'logs.releasedAt',
        'slot.id',
        'slot.slotNumber',
        'plot.id',
        'plot.name',
      ]);

    // If user is not admin, only show their guests
    if (user && user.role !== UserRole.ADMIN) {
      queryBuilder.where('guest.userId = :userId', { userId: user.id });
    }

    if (search) {
      queryBuilder.andWhere(
        '(guest.name LIKE :search OR guest.vehicleNumber LIKE :search OR guest.contact LIKE :search)',
        { search: `%${search}%` }
      );
    }

    const [data, total] = await queryBuilder
      .skip(skip)
      .take(limit)
      .orderBy('guest.expectedVisitTime', 'DESC')
      .getManyAndCount();

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: number, userId?: number): Promise<Guest> {
    const queryBuilder = this.guestRepository.createQueryBuilder('guest')
      .leftJoinAndSelect('guest.user', 'user')
      .leftJoinAndSelect('guest.parkingLogs', 'logs')
      .leftJoinAndSelect('logs.slot', 'slot')
      .leftJoinAndSelect('slot.plot', 'plot')
      .where('guest.id = :id', { id });

    // If userId is provided and user is not admin, check ownership
    if (userId) {
      const user = await this.guestRepository.manager.findOne(User, { where: { id: userId } });
      if (user && user.role !== UserRole.ADMIN) {
        queryBuilder.andWhere('guest.userId = :userId', { userId });
      }
    }

    const guest = await queryBuilder.getOne();

    if (!guest) {
      throw new NotFoundException('Guest not found');
    }

    return guest;
  }

  async update(id: number, updateGuestDto: UpdateGuestDto, userId: number): Promise<Guest> {
    const guest = await this.findOne(id, userId);

    // Check ownership (unless admin)
    const user = await this.guestRepository.manager.findOne(User, { where: { id: userId } });
    if (user?.role !== UserRole.ADMIN && guest.userId !== userId) {
      throw new ForbiddenException('You can only update your own guests');
    }

    // Convert date strings to Date objects
    const updateData = { ...updateGuestDto };
    if (updateData.expectedVisitTime) {
      updateData.expectedVisitTime = new Date(updateData.expectedVisitTime) as any;
    }
    if (updateData.actualArrivalTime) {
      updateData.actualArrivalTime = new Date(updateData.actualArrivalTime) as any;
    }
    if (updateData.actualDepartureTime) {
      updateData.actualDepartureTime = new Date(updateData.actualDepartureTime) as any;
    }

    Object.assign(guest, updateData);
    return this.guestRepository.save(guest);
  }

  async remove(id: number, userId: number): Promise<void> {
    const guest = await this.findOne(id, userId);

    // Check ownership (unless admin)
    const user = await this.guestRepository.manager.findOne(User, { where: { id: userId } });
    if (user?.role !== UserRole.ADMIN && guest.userId !== userId) {
      throw new ForbiddenException('You can only delete your own guests');
    }

    await this.guestRepository.remove(guest);
  }

  async findUserGuests(userId: number): Promise<Guest[]> {
    return this.guestRepository.find({
      where: { userId },
      relations: ['parkingLogs', 'parkingLogs.slot', 'parkingLogs.slot.plot'],
      order: { expectedVisitTime: 'DESC' },
    });
  }

  async findExpectedGuests(userId?: number): Promise<Guest[]> {
    const queryBuilder = this.guestRepository.createQueryBuilder('guest')
      .leftJoinAndSelect('guest.user', 'user')
      .where('guest.status = :status', { status: GuestStatus.EXPECTED })
      .andWhere('guest.expectedVisitTime >= :today', { today: new Date().toISOString().split('T')[0] });

    if (userId) {
      const user = await this.guestRepository.manager.findOne(User, { where: { id: userId } });
      if (user && user.role !== UserRole.ADMIN) {
        queryBuilder.andWhere('guest.userId = :userId', { userId });
      }
    }

    return queryBuilder
      .orderBy('guest.expectedVisitTime', 'ASC')
      .getMany();
  }

  async findOverdueGuests(userId?: number): Promise<Guest[]> {
    const queryBuilder = this.guestRepository.createQueryBuilder('guest')
      .leftJoinAndSelect('guest.user', 'user')
      .where('guest.status = :status', { status: GuestStatus.EXPECTED })
      .andWhere('guest.expectedVisitTime < :now', { now: new Date() });

    if (userId) {
      const user = await this.guestRepository.manager.findOne(User, { where: { id: userId } });
      if (user && user.role !== UserRole.ADMIN) {
        queryBuilder.andWhere('guest.userId = :userId', { userId });
      }
    }

    return queryBuilder
      .orderBy('guest.expectedVisitTime', 'ASC')
      .getMany();
  }

  async markArrived(id: number, userId: number): Promise<Guest> {
    const guest = await this.findOne(id, userId);

    // Check ownership (unless admin)
    const user = await this.guestRepository.manager.findOne(User, { where: { id: userId } });
    if (user?.role !== UserRole.ADMIN && guest.userId !== userId) {
      throw new ForbiddenException('You can only update your own guests');
    }

    guest.status = GuestStatus.ARRIVED;
    guest.actualArrivalTime = new Date();

    return this.guestRepository.save(guest);
  }

  async markDeparted(id: number, userId: number): Promise<Guest> {
    const guest = await this.findOne(id, userId);

    // Check ownership (unless admin)
    const user = await this.guestRepository.manager.findOne(User, { where: { id: userId } });
    if (user?.role !== UserRole.ADMIN && guest.userId !== userId) {
      throw new ForbiddenException('You can only update your own guests');
    }

    guest.status = GuestStatus.DEPARTED;
    guest.actualDepartureTime = new Date();

    return this.guestRepository.save(guest);
  }
}
