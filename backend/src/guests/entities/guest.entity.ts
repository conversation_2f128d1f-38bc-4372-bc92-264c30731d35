import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';

import { User } from '../../users/entities/user.entity';
import { ParkingLog } from '../../parking-logs/entities/parking-log.entity';

export enum GuestStatus {
  EXPECTED = 'Expected',
  ARRIVED = 'Arrived',
  DEPARTED = 'Departed',
}

@Entity('guests')
@Index(['userId'])
@Index(['status'])
@Index(['expectedVisitTime'])
@Index(['vehicleNumber'])
export class Guest {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  userId: number;

  @Column({ length: 255 })
  name: string;

  @Column({ length: 20 })
  contact: string;

  @Column({ length: 50 })
  vehicleNumber: string;

  @Column({ length: 255 })
  purpose: string;

  @Column({ type: 'datetime' })
  expectedVisitTime: Date;

  @Column({ type: 'datetime', nullable: true })
  actualArrivalTime: Date;

  @Column({ type: 'datetime', nullable: true })
  actualDepartureTime: Date;

  @Column({
    type: 'enum',
    enum: GuestStatus,
    default: GuestStatus.EXPECTED,
  })
  status: GuestStatus;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => User, (user) => user.guests, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @OneToMany(() => ParkingLog, (log) => log.guest)
  parkingLogs: ParkingLog[];

  // Virtual properties
  get isExpected(): boolean {
    return this.status === GuestStatus.EXPECTED;
  }

  get hasArrived(): boolean {
    return this.status === GuestStatus.ARRIVED;
  }

  get hasDeparted(): boolean {
    return this.status === GuestStatus.DEPARTED;
  }

  get visitDuration(): number | null {
    if (!this.actualArrivalTime || !this.actualDepartureTime) {
      return null;
    }
    return this.actualDepartureTime.getTime() - this.actualArrivalTime.getTime();
  }

  get isOverdue(): boolean {
    if (this.status !== GuestStatus.EXPECTED) {
      return false;
    }
    return new Date() > this.expectedVisitTime;
  }
}
