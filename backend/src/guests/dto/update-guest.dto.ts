import { PartialType } from '@nestjs/mapped-types';
import { IsOptional, IsDateString } from 'class-validator';
import { CreateGuestDto } from './create-guest.dto';

export class UpdateGuestDto extends PartialType(CreateGuestDto) {
  @IsOptional()
  @IsDateString({}, { message: 'Actual arrival time must be a valid date' })
  actualArrivalTime?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Actual departure time must be a valid date' })
  actualDepartureTime?: string;
}
