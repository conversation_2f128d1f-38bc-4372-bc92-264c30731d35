import { IsNotEmpty, IsString, IsDateString, IsEnum, IsOptional } from 'class-validator';
import { GuestStatus } from '../entities/guest.entity';

export class CreateGuestDto {
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name is required' })
  name: string;

  @IsString({ message: 'Contact must be a string' })
  @IsNotEmpty({ message: 'Contact is required' })
  contact: string;

  @IsString({ message: 'Vehicle number must be a string' })
  @IsNotEmpty({ message: 'Vehicle number is required' })
  vehicleNumber: string;

  @IsString({ message: 'Purpose must be a string' })
  @IsNotEmpty({ message: 'Purpose is required' })
  purpose: string;

  @IsDateString({}, { message: 'Expected visit time must be a valid date' })
  @IsNotEmpty({ message: 'Expected visit time is required' })
  expectedVisitTime: string;

  @IsOptional()
  @IsEnum(GuestStatus, { message: 'Status must be one of: Expected, Arrived, Departed' })
  status?: GuestStatus;
}
