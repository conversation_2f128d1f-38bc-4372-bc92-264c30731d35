import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  ParseIntPipe,
  ValidationPipe,
} from '@nestjs/common';

import { GuestsService } from './guests.service';
import { CreateGuestDto } from './dto/create-guest.dto';
import { UpdateGuestDto } from './dto/update-guest.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User, UserRole } from '../users/entities/user.entity';
import { PaginationOptions } from '../users/users.service';

@Controller('guests')
@UseGuards(JwtAuthGuard, RolesGuard)
export class GuestsController {
  constructor(private readonly guestsService: GuestsService) {}

  @Post()
  @Roles(UserRole.USER, UserRole.ADMIN)
  async create(
    @Body(ValidationPipe) createGuestDto: CreateGuestDto,
    @CurrentUser() user: User,
  ) {
    return this.guestsService.create(createGuestDto, user.id);
  }

  @Get()
  async findAll(
    @Query('page', ParseIntPipe) page?: number,
    @Query('limit', ParseIntPipe) limit?: number,
    @Query('search') search?: string,
    @CurrentUser() user?: User,
  ) {
    const options: PaginationOptions = { page, limit, search };
    return this.guestsService.findAll(options, user);
  }

  @Get('my-guests')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async findMyGuests(@CurrentUser() user: User) {
    return this.guestsService.findUserGuests(user.id);
  }

  @Get('expected')
  async findExpectedGuests(@CurrentUser() user: User) {
    return this.guestsService.findExpectedGuests(user.id);
  }

  @Get('overdue')
  async findOverdueGuests(@CurrentUser() user: User) {
    return this.guestsService.findOverdueGuests(user.id);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ) {
    return this.guestsService.findOne(id, user.id);
  }

  @Patch(':id')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body(ValidationPipe) updateGuestDto: UpdateGuestDto,
    @CurrentUser() user: User,
  ) {
    return this.guestsService.update(id, updateGuestDto, user.id);
  }

  @Post(':id/arrived')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async markArrived(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ) {
    return this.guestsService.markArrived(id, user.id);
  }

  @Post(':id/departed')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async markDeparted(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ) {
    return this.guestsService.markDeparted(id, user.id);
  }

  @Delete(':id')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ) {
    await this.guestsService.remove(id, user.id);
    return { message: 'Guest deleted successfully' };
  }
}
