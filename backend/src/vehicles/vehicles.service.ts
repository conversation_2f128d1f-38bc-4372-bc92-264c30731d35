import { Injectable, NotFoundException, ConflictException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Vehicle } from './entities/vehicle.entity';
import { CreateVehicleDto } from './dto/create-vehicle.dto';
import { UpdateVehicleDto } from './dto/update-vehicle.dto';
import { User, UserRole } from '../users/entities/user.entity';
import { PaginationOptions, PaginatedResult } from '../users/users.service';

@Injectable()
export class VehiclesService {
  constructor(
    @InjectRepository(Vehicle)
    private readonly vehicleRepository: Repository<Vehicle>,
  ) {}

  async create(createVehicleDto: CreateVehicleDto, userId: number): Promise<Vehicle> {
    // Check if vehicle number already exists
    const existingVehicle = await this.vehicleRepository.findOne({
      where: { vehicleNumber: createVehicleDto.vehicleNumber },
    });

    if (existingVehicle) {
      throw new ConflictException('Vehicle number already exists');
    }

    const vehicle = this.vehicleRepository.create({
      ...createVehicleDto,
      userId,
    });

    return this.vehicleRepository.save(vehicle);
  }

  async findAll(options: PaginationOptions = {}, user?: User): Promise<PaginatedResult<Vehicle>> {
    const { page = 1, limit = 10, search } = options;
    const skip = (page - 1) * limit;

    const queryBuilder = this.vehicleRepository.createQueryBuilder('vehicle')
      .leftJoinAndSelect('vehicle.user', 'user')
      .leftJoinAndSelect('vehicle.parkingSlots', 'slots')
      .leftJoinAndSelect('slots.plot', 'plot')
      .select([
        'vehicle.id',
        'vehicle.vehicleNumber',
        'vehicle.ownerName',
        'vehicle.type',
        'vehicle.contact',
        'vehicle.isActive',
        'vehicle.createdAt',
        'vehicle.updatedAt',
        'user.id',
        'user.firstName',
        'user.lastName',
        'slots.id',
        'slots.slotNumber',
        'slots.status',
        'plot.id',
        'plot.name',
      ]);

    // If user is not admin, only show their vehicles
    if (user && user.role !== UserRole.ADMIN) {
      queryBuilder.where('vehicle.userId = :userId', { userId: user.id });
    }

    if (search) {
      queryBuilder.andWhere(
        '(vehicle.vehicleNumber LIKE :search OR vehicle.ownerName LIKE :search OR vehicle.contact LIKE :search)',
        { search: `%${search}%` }
      );
    }

    const [data, total] = await queryBuilder
      .skip(skip)
      .take(limit)
      .orderBy('vehicle.createdAt', 'DESC')
      .getManyAndCount();

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: number, userId?: number): Promise<Vehicle> {
    const queryBuilder = this.vehicleRepository.createQueryBuilder('vehicle')
      .leftJoinAndSelect('vehicle.user', 'user')
      .leftJoinAndSelect('vehicle.parkingSlots', 'slots')
      .leftJoinAndSelect('slots.plot', 'plot')
      .leftJoinAndSelect('vehicle.parkingLogs', 'logs')
      .where('vehicle.id = :id', { id });

    // If userId is provided and user is not admin, check ownership
    if (userId) {
      const user = await this.vehicleRepository.manager.findOne(User, { where: { id: userId } });
      if (user && user.role !== UserRole.ADMIN) {
        queryBuilder.andWhere('vehicle.userId = :userId', { userId });
      }
    }

    const vehicle = await queryBuilder.getOne();

    if (!vehicle) {
      throw new NotFoundException('Vehicle not found');
    }

    return vehicle;
  }

  async update(id: number, updateVehicleDto: UpdateVehicleDto, userId: number): Promise<Vehicle> {
    const vehicle = await this.findOne(id, userId);

    // Check ownership (unless admin)
    const user = await this.vehicleRepository.manager.findOne(User, { where: { id: userId } });
    if (user?.role !== UserRole.ADMIN && vehicle.userId !== userId) {
      throw new ForbiddenException('You can only update your own vehicles');
    }

    Object.assign(vehicle, updateVehicleDto);
    return this.vehicleRepository.save(vehicle);
  }

  async remove(id: number, userId: number): Promise<void> {
    const vehicle = await this.findOne(id, userId);

    // Check ownership (unless admin)
    const user = await this.vehicleRepository.manager.findOne(User, { where: { id: userId } });
    if (user?.role !== UserRole.ADMIN && vehicle.userId !== userId) {
      throw new ForbiddenException('You can only delete your own vehicles');
    }

    // Check if vehicle is currently parked
    if (vehicle.isCurrentlyParked) {
      throw new BadRequestException('Cannot delete vehicle that is currently parked');
    }

    await this.vehicleRepository.remove(vehicle);
  }

  async findUserVehicles(userId: number): Promise<Vehicle[]> {
    return this.vehicleRepository.find({
      where: { userId, isActive: true },
      relations: ['parkingSlots', 'parkingSlots.plot'],
      order: { createdAt: 'DESC' },
    });
  }

  async findAvailableVehicles(userId: number): Promise<Vehicle[]> {
    return this.vehicleRepository.createQueryBuilder('vehicle')
      .leftJoin('vehicle.parkingSlots', 'slots')
      .where('vehicle.userId = :userId', { userId })
      .andWhere('vehicle.isActive = :isActive', { isActive: true })
      .andWhere('(slots.id IS NULL OR slots.status != :occupied)', { occupied: 'Occupied' })
      .getMany();
  }
}
