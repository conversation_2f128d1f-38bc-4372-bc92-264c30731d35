import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  ParseIntPipe,
  ValidationPipe,
} from '@nestjs/common';

import { VehiclesService } from './vehicles.service';
import { CreateVehicleDto } from './dto/create-vehicle.dto';
import { UpdateVehicleDto } from './dto/update-vehicle.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User, UserRole } from '../users/entities/user.entity';
import { PaginationOptions } from '../users/users.service';

@Controller('vehicles')
@UseGuards(JwtAuthGuard, RolesGuard)
export class VehiclesController {
  constructor(private readonly vehiclesService: VehiclesService) {}

  @Post()
  @Roles(UserRole.USER, UserRole.ADMIN)
  async create(
    @Body(ValidationPipe) createVehicleDto: CreateVehicleDto,
    @CurrentUser() user: User,
  ) {
    return this.vehiclesService.create(createVehicleDto, user.id);
  }

  @Get()
  async findAll(
    @Query('page', ParseIntPipe) page?: number,
    @Query('limit', ParseIntPipe) limit?: number,
    @Query('search') search?: string,
    @CurrentUser() user?: User,
  ) {
    const options: PaginationOptions = { page, limit, search };
    return this.vehiclesService.findAll(options, user);
  }

  @Get('my-vehicles')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async findMyVehicles(@CurrentUser() user: User) {
    return this.vehiclesService.findUserVehicles(user.id);
  }

  @Get('available')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async findAvailableVehicles(@CurrentUser() user: User) {
    return this.vehiclesService.findAvailableVehicles(user.id);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ) {
    return this.vehiclesService.findOne(id, user.id);
  }

  @Patch(':id')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body(ValidationPipe) updateVehicleDto: UpdateVehicleDto,
    @CurrentUser() user: User,
  ) {
    return this.vehiclesService.update(id, updateVehicleDto, user.id);
  }

  @Delete(':id')
  @Roles(UserRole.USER, UserRole.ADMIN)
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ) {
    await this.vehiclesService.remove(id, user.id);
    return { message: 'Vehicle deleted successfully' };
  }
}
