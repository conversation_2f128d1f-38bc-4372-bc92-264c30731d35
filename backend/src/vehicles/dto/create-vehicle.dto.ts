import { IsNotEmpty, IsString, IsE<PERSON> } from 'class-validator';
import { VehicleType } from '../entities/vehicle.entity';

export class CreateVehicleDto {
  @IsString({ message: 'Vehicle number must be a string' })
  @IsNotEmpty({ message: 'Vehicle number is required' })
  vehicleNumber: string;

  @IsString({ message: 'Owner name must be a string' })
  @IsNotEmpty({ message: 'Owner name is required' })
  ownerName: string;

  @IsEnum(VehicleType, { message: 'Type must be one of: Car, Motorcycle, Truck, Van' })
  @IsNotEmpty({ message: 'Type is required' })
  type: VehicleType;

  @IsString({ message: 'Contact must be a string' })
  @IsNotEmpty({ message: 'Contact is required' })
  contact: string;
}
