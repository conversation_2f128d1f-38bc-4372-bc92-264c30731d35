import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';

import { User } from '../../users/entities/user.entity';
import { ParkingSlot } from '../../parking-slots/entities/parking-slot.entity';
import { ParkingLog } from '../../parking-logs/entities/parking-log.entity';

export enum VehicleType {
  CAR = 'Car',
  MOTORCYCLE = 'Motorcycle',
  TRUCK = 'Truck',
  VAN = 'Van',
}

@Entity('vehicles')
@Index(['userId'])
@Index(['vehicleNumber'])
@Index(['type'])
@Index(['isActive'])
export class Vehicle {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  userId: number;

  @Column({ unique: true, length: 50 })
  vehicleNumber: string;

  @Column({ length: 255 })
  ownerName: string;

  @Column({
    type: 'enum',
    enum: VehicleType,
  })
  type: VehicleType;

  @Column({ length: 20 })
  contact: string;

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => User, (user) => user.vehicles, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @OneToMany(() => ParkingSlot, (slot) => slot.vehicle)
  parkingSlots: ParkingSlot[];

  @OneToMany(() => ParkingLog, (log) => log.vehicle)
  parkingLogs: ParkingLog[];

  // Virtual properties
  get currentParkingSlot(): ParkingSlot | null {
    if (!this.parkingSlots) return null;
    return this.parkingSlots.find(slot => slot.status === 'Occupied') || null;
  }

  get isCurrentlyParked(): boolean {
    return this.currentParkingSlot !== null;
  }
}
