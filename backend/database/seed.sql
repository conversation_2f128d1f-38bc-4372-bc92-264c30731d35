-- Parking Management System - Sample Data
-- Run this after creating the schema

USE parking_management;

-- Sample plot owner users
INSERT INTO users (email, password, firstName, lastName, phone, role) VALUES 
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'John', 'Doe', '+1234567890', 'user'),
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', '<PERSON>', 'Smith', '+1234567891', 'user'),
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', '<PERSON>', '<PERSON>', '+1234567892', 'user');
-- Password for all users is 'admin123'

-- <PERSON><PERSON> plots
INSERT INTO plots (userId, name, type, location, totalSlots, description) VALUES 
(2, 'Downtown Mall Parking', 'Mall', '123 Main Street, Downtown', 50, 'Large parking area for shopping mall visitors'),
(2, 'Residential Complex A', 'Residential', '456 Oak Avenue, Suburbs', 25, 'Private parking for apartment residents'),
(3, 'Office Building Parking', 'Office', '789 Business Blvd, Business District', 30, 'Employee and visitor parking for office building'),
(4, 'Apartment Block B', 'Apartment', '321 Pine Street, Midtown', 40, 'Parking spaces for apartment tenants and guests');

-- Sample vehicles
INSERT INTO vehicles (userId, vehicleNumber, ownerName, type, contact) VALUES 
(2, 'ABC-123', 'John Doe', 'Car', '+1234567890'),
(2, 'XYZ-789', 'Sarah Doe', 'Car', '+1234567893'),
(3, 'DEF-456', 'Jane Smith', 'Car', '+1234567891'),
(3, 'GHI-012', 'Bob Smith', 'Motorcycle', '+1234567894'),
(4, 'JKL-345', 'Mike Wilson', 'Car', '+1234567892'),
(4, 'MNO-678', 'Lisa Wilson', 'Van', '+1234567895');

-- Sample parking slots for each plot
-- Plot 1 (Downtown Mall) - 50 slots
INSERT INTO parking_slots (plotId, slotNumber, status) VALUES 
(1, 'A1', 'Available'), (1, 'A2', 'Available'), (1, 'A3', 'Available'), (1, 'A4', 'Available'), (1, 'A5', 'Available'),
(1, 'A6', 'Available'), (1, 'A7', 'Available'), (1, 'A8', 'Available'), (1, 'A9', 'Available'), (1, 'A10', 'Available'),
(1, 'B1', 'Available'), (1, 'B2', 'Available'), (1, 'B3', 'Available'), (1, 'B4', 'Available'), (1, 'B5', 'Available'),
(1, 'B6', 'Available'), (1, 'B7', 'Available'), (1, 'B8', 'Available'), (1, 'B9', 'Available'), (1, 'B10', 'Available'),
(1, 'C1', 'Available'), (1, 'C2', 'Available'), (1, 'C3', 'Available'), (1, 'C4', 'Available'), (1, 'C5', 'Available'),
(1, 'C6', 'Available'), (1, 'C7', 'Available'), (1, 'C8', 'Available'), (1, 'C9', 'Available'), (1, 'C10', 'Available'),
(1, 'D1', 'Available'), (1, 'D2', 'Available'), (1, 'D3', 'Available'), (1, 'D4', 'Available'), (1, 'D5', 'Available'),
(1, 'D6', 'Available'), (1, 'D7', 'Available'), (1, 'D8', 'Available'), (1, 'D9', 'Available'), (1, 'D10', 'Available'),
(1, 'E1', 'Available'), (1, 'E2', 'Available'), (1, 'E3', 'Available'), (1, 'E4', 'Available'), (1, 'E5', 'Available'),
(1, 'E6', 'Available'), (1, 'E7', 'Available'), (1, 'E8', 'Available'), (1, 'E9', 'Available'), (1, 'E10', 'Available');

-- Plot 2 (Residential Complex A) - 25 slots
INSERT INTO parking_slots (plotId, slotNumber, status) VALUES 
(2, '1', 'Available'), (2, '2', 'Available'), (2, '3', 'Available'), (2, '4', 'Available'), (2, '5', 'Available'),
(2, '6', 'Available'), (2, '7', 'Available'), (2, '8', 'Available'), (2, '9', 'Available'), (2, '10', 'Available'),
(2, '11', 'Available'), (2, '12', 'Available'), (2, '13', 'Available'), (2, '14', 'Available'), (2, '15', 'Available'),
(2, '16', 'Available'), (2, '17', 'Available'), (2, '18', 'Available'), (2, '19', 'Available'), (2, '20', 'Available'),
(2, '21', 'Available'), (2, '22', 'Available'), (2, '23', 'Available'), (2, '24', 'Available'), (2, '25', 'Available');

-- Plot 3 (Office Building) - 30 slots
INSERT INTO parking_slots (plotId, slotNumber, status) VALUES 
(3, 'P1', 'Available'), (3, 'P2', 'Available'), (3, 'P3', 'Available'), (3, 'P4', 'Available'), (3, 'P5', 'Available'),
(3, 'P6', 'Available'), (3, 'P7', 'Available'), (3, 'P8', 'Available'), (3, 'P9', 'Available'), (3, 'P10', 'Available'),
(3, 'P11', 'Available'), (3, 'P12', 'Available'), (3, 'P13', 'Available'), (3, 'P14', 'Available'), (3, 'P15', 'Available'),
(3, 'P16', 'Available'), (3, 'P17', 'Available'), (3, 'P18', 'Available'), (3, 'P19', 'Available'), (3, 'P20', 'Available'),
(3, 'P21', 'Available'), (3, 'P22', 'Available'), (3, 'P23', 'Available'), (3, 'P24', 'Available'), (3, 'P25', 'Available'),
(3, 'P26', 'Available'), (3, 'P27', 'Available'), (3, 'P28', 'Available'), (3, 'P29', 'Available'), (3, 'P30', 'Available');

-- Plot 4 (Apartment Block B) - 40 slots
INSERT INTO parking_slots (plotId, slotNumber, status) VALUES 
(4, 'S1', 'Available'), (4, 'S2', 'Available'), (4, 'S3', 'Available'), (4, 'S4', 'Available'), (4, 'S5', 'Available'),
(4, 'S6', 'Available'), (4, 'S7', 'Available'), (4, 'S8', 'Available'), (4, 'S9', 'Available'), (4, 'S10', 'Available'),
(4, 'S11', 'Available'), (4, 'S12', 'Available'), (4, 'S13', 'Available'), (4, 'S14', 'Available'), (4, 'S15', 'Available'),
(4, 'S16', 'Available'), (4, 'S17', 'Available'), (4, 'S18', 'Available'), (4, 'S19', 'Available'), (4, 'S20', 'Available'),
(4, 'S21', 'Available'), (4, 'S22', 'Available'), (4, 'S23', 'Available'), (4, 'S24', 'Available'), (4, 'S25', 'Available'),
(4, 'S26', 'Available'), (4, 'S27', 'Available'), (4, 'S28', 'Available'), (4, 'S29', 'Available'), (4, 'S30', 'Available'),
(4, 'S31', 'Available'), (4, 'S32', 'Available'), (4, 'S33', 'Available'), (4, 'S34', 'Available'), (4, 'S35', 'Available'),
(4, 'S36', 'Available'), (4, 'S37', 'Available'), (4, 'S38', 'Available'), (4, 'S39', 'Available'), (4, 'S40', 'Available');

-- Sample guests
INSERT INTO guests (userId, name, contact, vehicleNumber, purpose, expectedVisitTime, status) VALUES 
(2, 'Alice Johnson', '+1234567896', 'GUEST-001', 'Business meeting', '2024-01-15 10:00:00', 'Expected'),
(3, 'Robert Brown', '+1234567897', 'GUEST-002', 'Family visit', '2024-01-15 14:30:00', 'Expected'),
(4, 'Emily Davis', '+1234567898', 'GUEST-003', 'Delivery service', '2024-01-15 09:15:00', 'Expected');

-- Sample parking assignments
UPDATE parking_slots SET status = 'Occupied', vehicleId = 1, allocatedAt = NOW() WHERE id = 1;
UPDATE parking_slots SET status = 'Occupied', vehicleId = 3, allocatedAt = NOW() WHERE id = 26;
UPDATE parking_slots SET status = 'Occupied', vehicleId = 5, allocatedAt = NOW() WHERE id = 81;

-- Sample parking logs
INSERT INTO parking_logs (slotId, vehicleId, action, assignedAt, notes) VALUES 
(1, 1, 'Assigned', NOW(), 'Regular parking assignment'),
(26, 3, 'Assigned', NOW(), 'Employee parking'),
(81, 5, 'Assigned', NOW(), 'Resident parking');
