#!/bin/bash

# Parking Management System Database Setup Script

echo "🚗 Setting up Parking Management System Database..."

# Check if MySQL is installed
if ! command -v mysql &> /dev/null; then
    echo "❌ MySQL is not installed. Please install MySQL 8.0+ first."
    exit 1
fi

# Default MySQL credentials (can be overridden with environment variables)
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-3306}
DB_USERNAME=${DB_USERNAME:-root}
DB_PASSWORD=${DB_PASSWORD:-password}
DB_DATABASE=${DB_DATABASE:-parking_management}

echo "📊 Creating database schema..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" < schema.sql

if [ $? -eq 0 ]; then
    echo "✅ Database schema created successfully!"
else
    echo "❌ Failed to create database schema."
    exit 1
fi

echo "🌱 Seeding sample data..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" < seed.sql

if [ $? -eq 0 ]; then
    echo "✅ Sample data seeded successfully!"
else
    echo "❌ Failed to seed sample data."
    exit 1
fi

echo "🎉 Database setup completed!"
echo ""
echo "📋 Default Admin Credentials:"
echo "   Email: <EMAIL>"
echo "   Password: admin123"
echo ""
echo "📋 Sample User Credentials:"
echo "   Email: <EMAIL>"
echo "   Password: admin123"
echo ""
echo "🚀 You can now start the backend server with: npm run start:dev"
