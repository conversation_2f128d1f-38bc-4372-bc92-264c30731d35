-- Parking Management System Database Schema
-- MySQL 8.0+

-- Create database
CREATE DATABASE IF NOT EXISTS parking_management;
USE parking_management;

-- Users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    firstName VARCHAR(100) NOT NULL,
    lastName VARCHAR(100) NOT NULL,
    phone VARCHAR(20) UNIQUE,
    role ENUM('admin', 'user') NOT NULL DEFAULT 'user',
    isActive BOOLEAN DEFAULT TRUE,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_active (isActive)
);

-- Plots table
CREATE TABLE plots (
    id INT PRIMARY KEY AUTO_INCREMENT,
    userId INT NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    type ENUM('Mall', 'Apartment', 'Office', 'Residential') NOT NULL,
    location TEXT NOT NULL,
    totalSlots INT NOT NULL CHECK (totalSlots >= 1),
    description TEXT,
    isActive BOOLEAN DEFAULT TRUE,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (userId),
    INDEX idx_type (type),
    INDEX idx_active (isActive)
);

-- Vehicles table
CREATE TABLE vehicles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    userId INT NOT NULL,
    vehicleNumber VARCHAR(50) UNIQUE NOT NULL,
    ownerName VARCHAR(255) NOT NULL,
    type ENUM('Car', 'Motorcycle', 'Truck', 'Van') NOT NULL,
    contact VARCHAR(20) NOT NULL,
    isActive BOOLEAN DEFAULT TRUE,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (userId),
    INDEX idx_vehicle_number (vehicleNumber),
    INDEX idx_type (type),
    INDEX idx_active (isActive)
);

-- Parking slots table
CREATE TABLE parking_slots (
    id INT PRIMARY KEY AUTO_INCREMENT,
    plotId INT NOT NULL,
    slotNumber VARCHAR(50) NOT NULL,
    status ENUM('Available', 'Occupied', 'Maintenance') DEFAULT 'Available',
    vehicleId INT NULL,
    allocatedAt TIMESTAMP NULL,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (plotId) REFERENCES plots(id) ON DELETE CASCADE,
    FOREIGN KEY (vehicleId) REFERENCES vehicles(id) ON DELETE SET NULL,
    UNIQUE KEY unique_plot_slot (plotId, slotNumber),
    INDEX idx_plot_id (plotId),
    INDEX idx_status (status),
    INDEX idx_vehicle_id (vehicleId)
);

-- Guests table
CREATE TABLE guests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    userId INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    contact VARCHAR(20) NOT NULL,
    vehicleNumber VARCHAR(50) NOT NULL,
    purpose VARCHAR(255) NOT NULL,
    expectedVisitTime DATETIME NOT NULL,
    actualArrivalTime DATETIME NULL,
    actualDepartureTime DATETIME NULL,
    status ENUM('Expected', 'Arrived', 'Departed') DEFAULT 'Expected',
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (userId),
    INDEX idx_status (status),
    INDEX idx_expected_visit (expectedVisitTime),
    INDEX idx_vehicle_number (vehicleNumber)
);

-- Parking logs table
CREATE TABLE parking_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    slotId INT NOT NULL,
    vehicleId INT NULL,
    guestId INT NULL,
    action ENUM('Assigned', 'Released') NOT NULL,
    assignedAt TIMESTAMP NOT NULL,
    releasedAt TIMESTAMP NULL,
    notes TEXT,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (slotId) REFERENCES parking_slots(id) ON DELETE CASCADE,
    FOREIGN KEY (vehicleId) REFERENCES vehicles(id) ON DELETE SET NULL,
    FOREIGN KEY (guestId) REFERENCES guests(id) ON DELETE SET NULL,
    INDEX idx_slot_id (slotId),
    INDEX idx_vehicle_id (vehicleId),
    INDEX idx_guest_id (guestId),
    INDEX idx_action (action),
    INDEX idx_assigned_at (assignedAt),
    
    CHECK (vehicleId IS NOT NULL OR guestId IS NOT NULL)
);

-- Insert default admin user
INSERT INTO users (email, password, firstName, lastName, role) VALUES 
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'System', 'Administrator', 'admin');
-- Default password is 'admin123' (hashed with bcrypt)
