# 🚗 Parking Management System

A comprehensive parking management system built with **NestJS** (backend) and **Angular 17** (frontend) featuring role-based access control, real-time parking slot management, and modern responsive design.

## 🏗️ Project Structure

```
parking/
├── backend/          # NestJS API server
├── frontend/         # Angular client application
└── README.md         # This file
```

## 🚀 Quick Start

### Prerequisites

- Node.js 20.19.0 or higher
- MySQL 8.0 or higher
- npm or yarn package manager

### Database Setup

1. Create a MySQL database named `parking_management`
2. Update the database credentials in `backend/.env`

### Backend Setup

```bash
cd backend
npm install
npm run start:dev
```

The backend API will be available at `http://localhost:3000`

### Frontend Setup

```bash
cd frontend
npm install
ng serve
```

The frontend application will be available at `http://localhost:4200`

## 🎯 Features

### Admin Features
- User management (CRUD operations)
- System-wide dashboard and analytics
- View all plots and parking slots
- Generate reports

### Plot Owner Features
- Manage personal plots and parking slots
- Vehicle registration and management
- Direct slot assignment (no approval needed)
- Guest management
- Parking history and logs

## 🔧 Tech Stack

### Backend
- **Framework:** NestJS 11+
- **Database:** MySQL 8+ with TypeORM
- **Authentication:** JWT with refresh tokens
- **Validation:** class-validator, class-transformer
- **Security:** bcryptjs, passport

### Frontend
- **Framework:** Angular 17+
- **UI Components:** Angular Material
- **Styling:** Tailwind CSS
- **State Management:** Angular Services
- **HTTP Client:** Angular HttpClient

## 📊 Database Schema

### Core Entities
- **Users** - Admin and plot owner accounts
- **Plots** - Parking plot information
- **ParkingSlots** - Individual parking spaces
- **Vehicles** - Registered vehicles
- **Guests** - Guest management
- **ParkingLogs** - Audit trail and history

## 🔐 Authentication & Authorization

- JWT-based authentication with refresh tokens
- Role-based access control (Admin vs Plot Owner)
- Protected routes and API endpoints
- Secure password hashing with bcryptjs

## 🧪 Testing

### Backend Testing
```bash
cd backend
npm run test          # Unit tests
npm run test:e2e      # End-to-end tests
npm run test:cov      # Coverage report
```

### Frontend Testing
```bash
cd frontend
ng test               # Unit tests
ng e2e                # End-to-end tests
```

## 📦 Deployment

### Production Build

**Backend:**
```bash
cd backend
npm run build
npm run start:prod
```

**Frontend:**
```bash
cd frontend
ng build --prod
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
