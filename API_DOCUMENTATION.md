# 📚 API Documentation - Parking Management System

This document provides comprehensive information about the REST API endpoints available in the Parking Management System.

## 🔗 Base URL
```
http://localhost:3000/api
```

## 🔐 Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Token Lifecycle
- **Access Token**: Expires in 15 minutes
- **Refresh Token**: Expires in 7 days
- **Auto-refresh**: Frontend automatically refreshes tokens

## 📋 API Endpoints

### 🔑 Authentication Endpoints

#### POST /auth/login
Authenticate user and receive JWT tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "Admin",
    "lastName": "User",
    "role": "admin",
    "isActive": true
  }
}
```

#### POST /auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### POST /auth/logout
Logout user and invalidate tokens.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "message": "Logged out successfully"
}
```

#### GET /auth/profile
Get current user profile.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "firstName": "Admin",
  "lastName": "User",
  "phone": "+1234567890",
  "role": "admin",
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

### 👥 User Management (Admin Only)

#### GET /users
Get paginated list of users.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `search` (optional): Search term

**Headers:** `Authorization: Bearer <admin-token>`

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "user",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "total": 50,
  "page": 1,
  "limit": 10,
  "totalPages": 5
}
```

#### POST /users
Create new user.

**Headers:** `Authorization: Bearer <admin-token>`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "New",
  "lastName": "User",
  "phone": "+1234567890",
  "role": "user"
}
```

#### GET /users/:id
Get user by ID.

#### PATCH /users/:id
Update user information.

#### DELETE /users/:id
Delete user.

### 🏢 Plot Management

#### GET /plots/my-plots
Get current user's plots.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
[
  {
    "id": 1,
    "name": "Downtown Mall Parking",
    "type": "Mall",
    "location": "123 Main St, Downtown",
    "totalSlots": 100,
    "description": "Main parking area for downtown mall",
    "isActive": true,
    "availableSlots": 75,
    "occupiedSlots": 25,
    "occupancyRate": 25.0,
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
]
```

#### POST /plots
Create new plot.

**Request Body:**
```json
{
  "name": "Office Complex Parking",
  "type": "Office",
  "location": "456 Business Ave",
  "totalSlots": 50,
  "description": "Employee parking area"
}
```

#### GET /plots/:id
Get plot details with parking slots.

#### PATCH /plots/:id
Update plot information.

#### DELETE /plots/:id
Delete plot and all associated slots.

### 🚗 Vehicle Management

#### GET /vehicles/my-vehicles
Get current user's vehicles.

**Response:**
```json
[
  {
    "id": 1,
    "vehicleNumber": "ABC-1234",
    "ownerName": "John Doe",
    "type": "Car",
    "contact": "+1234567890",
    "isActive": true,
    "isCurrentlyParked": false,
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
]
```

#### POST /vehicles
Register new vehicle.

**Request Body:**
```json
{
  "vehicleNumber": "XYZ-5678",
  "ownerName": "Jane Smith",
  "type": "Car",
  "contact": "+0987654321"
}
```

#### GET /vehicles/available
Get vehicles available for parking assignment.

### 🅿️ Parking Slot Management

#### GET /parking-slots
Get parking slots (optionally filtered by plot).

**Query Parameters:**
- `plotId` (optional): Filter by plot ID

#### GET /parking-slots/available
Get available parking slots for a plot.

**Query Parameters:**
- `plotId` (required): Plot ID

#### POST /parking-slots/:id/assign
Assign vehicle or guest to parking slot.

**Request Body:**
```json
{
  "vehicleId": 1,
  "notes": "Regular parking assignment"
}
```

**Or for guest:**
```json
{
  "guestId": 1,
  "notes": "Visitor parking"
}
```

#### POST /parking-slots/:id/release
Release parking slot.

**Request Body:**
```json
{
  "notes": "Vehicle departed"
}
```

### 👥 Guest Management

#### GET /guests/my-guests
Get current user's guests.

#### GET /guests/expected
Get expected guests for today.

#### GET /guests/overdue
Get overdue guests.

#### POST /guests
Register new guest.

**Request Body:**
```json
{
  "name": "Visitor Name",
  "contact": "+1234567890",
  "vehicleNumber": "GUEST-123",
  "purpose": "Business meeting",
  "expectedVisitTime": "2024-01-01T14:00:00.000Z"
}
```

#### POST /guests/:id/arrived
Mark guest as arrived.

#### POST /guests/:id/departed
Mark guest as departed.

### 📊 Admin Dashboard

#### GET /admin/dashboard/stats
Get dashboard statistics.

**Headers:** `Authorization: Bearer <admin-token>`

**Response:**
```json
{
  "totalUsers": 150,
  "totalPlots": 25,
  "totalSlots": 1250,
  "occupiedSlots": 875,
  "availableSlots": 375,
  "occupancyRate": 70.0,
  "recentActivity": [...],
  "monthlyStats": {
    "assignmentLogs": 450,
    "releaseLogs": 420,
    "vehicleLogs": 300,
    "guestLogs": 150,
    "averageParkingDurationMinutes": 180
  }
}
```

#### GET /admin/system-overview
Get system overview with plot types and user statistics.

### 📝 Parking Logs

#### GET /parking-logs
Get parking activity logs.

**Query Parameters:**
- `page`, `limit`: Pagination
- `search`: Search term
- `startDate`, `endDate`: Date range filter
- `plotId`, `vehicleId`, `guestId`: Entity filters
- `action`: Filter by action type

#### GET /parking-logs/my-logs
Get current user's parking logs.

#### GET /parking-logs/statistics
Get parking statistics.

#### GET /parking-logs/recent-activity
Get recent parking activity.

## 🔒 Authorization Levels

### Public Endpoints
- `POST /auth/login`
- `GET /health`

### User Endpoints (Requires Authentication)
- All `/plots/my-*` endpoints
- All `/vehicles/my-*` endpoints
- All `/guests/my-*` endpoints
- `GET /parking-logs/my-logs`

### Admin Endpoints (Admin Role Required)
- All `/admin/*` endpoints
- All `/users/*` endpoints (except profile)
- `GET /parking-logs` (all logs)

## 📋 Response Formats

### Success Response
```json
{
  "data": {...},
  "message": "Operation successful"
}
```

### Error Response
```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request"
}
```

### Validation Error
```json
{
  "statusCode": 400,
  "message": [
    "email must be a valid email",
    "password must be longer than or equal to 6 characters"
  ],
  "error": "Bad Request"
}
```

## 🔧 Status Codes

- `200` - OK
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `500` - Internal Server Error

## 📝 Data Models

### User Model
```typescript
{
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: 'admin' | 'user';
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

### Plot Model
```typescript
{
  id: number;
  name: string;
  type: 'Mall' | 'Apartment' | 'Office' | 'Residential';
  location: string;
  totalSlots: number;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

### Vehicle Model
```typescript
{
  id: number;
  vehicleNumber: string;
  ownerName: string;
  type: 'Car' | 'Motorcycle' | 'Truck' | 'Van';
  contact: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

### Parking Slot Model
```typescript
{
  id: number;
  slotNumber: string;
  status: 'Available' | 'Occupied' | 'Maintenance';
  vehicleId?: number;
  allocatedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

## 🧪 Testing the API

### Using cURL

**Login:**
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

**Get User Plots:**
```bash
curl -X GET http://localhost:3000/api/plots/my-plots \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Using Postman

1. Import the API collection (if available)
2. Set up environment variables:
   - `baseUrl`: `http://localhost:3000/api`
   - `token`: Your JWT token
3. Use `{{baseUrl}}` and `{{token}}` in requests

## 🔄 Rate Limiting

The API implements rate limiting to prevent abuse:
- **General endpoints**: 100 requests per 15 minutes
- **Auth endpoints**: 5 requests per 15 minutes
- **Admin endpoints**: 200 requests per 15 minutes

## 📞 Support

For API-related questions:
- Check the error response for detailed information
- Verify authentication tokens are valid
- Ensure proper request format and required fields
- Review this documentation for endpoint specifications

---

**API Version**: 1.0.0  
**Last Updated**: January 2024
