# 🚀 Installation Guide - Parking Management System

This guide will help you set up the Parking Management System on your local development environment.

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

### Required Software
- **Node.js** 18.0 or higher ([Download](https://nodejs.org/))
- **npm** 9.0 or higher (comes with Node.js)
- **MySQL** 8.0 or higher ([Download](https://dev.mysql.com/downloads/mysql/))
- **Git** ([Download](https://git-scm.com/downloads))

### Optional (for Docker deployment)
- **Docker** ([Download](https://www.docker.com/get-started))
- **Docker Compose** (included with Docker Desktop)

## 🔧 Installation Methods

Choose one of the following installation methods:

### Method 1: Quick Start (Recommended for Development)

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd parking-management-system
   ```

2. **Run the Setup Script**
   
   **For Linux/macOS:**
   ```bash
   ./start-dev.sh
   ```
   
   **For Windows:**
   ```batch
   start-dev.bat
   ```

3. **Access the Application**
   - Frontend: http://localhost:4200
   - Backend API: http://localhost:3000

### Method 2: Manual Installation

#### Step 1: Database Setup

1. **Start MySQL Service**
   ```bash
   # Linux/macOS
   sudo systemctl start mysql
   # or
   brew services start mysql
   
   # Windows
   net start mysql
   ```

2. **Create Database and User**
   ```sql
   mysql -u root -p
   
   CREATE DATABASE parking_management;
   CREATE USER 'parking_user'@'localhost' IDENTIFIED BY 'parking_password';
   GRANT ALL PRIVILEGES ON parking_management.* TO 'parking_user'@'localhost';
   FLUSH PRIVILEGES;
   EXIT;
   ```

3. **Import Database Schema**
   ```bash
   mysql -u parking_user -p parking_management < database/schema.sql
   mysql -u parking_user -p parking_management < database/sample-data.sql
   ```

#### Step 2: Backend Setup

1. **Navigate to Backend Directory**
   ```bash
   cd backend
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Configure Environment**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your database credentials:
   ```env
   DB_HOST=localhost
   DB_PORT=3306
   DB_USERNAME=parking_user
   DB_PASSWORD=parking_password
   DB_DATABASE=parking_management
   ```

4. **Start Backend Server**
   ```bash
   npm run start:dev
   ```

#### Step 3: Frontend Setup

1. **Open New Terminal and Navigate to Frontend**
   ```bash
   cd frontend
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Install Angular CLI (if not installed globally)**
   ```bash
   npm install -g @angular/cli
   ```

4. **Start Frontend Server**
   ```bash
   ng serve
   ```

### Method 3: Docker Installation

1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd parking-management-system
   ```

2. **Start with Docker Compose**
   ```bash
   docker-compose up -d
   ```

3. **Access Application**
   - Frontend: http://localhost:4200
   - Backend API: http://localhost:3000
   - MySQL: localhost:3306

## 🔑 Default Login Credentials

After successful installation, use these credentials to log in:

### Admin Account
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Access**: Full system administration

### Regular User Account
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Access**: Plot owner features

## ✅ Verification

### 1. Check Backend Health
```bash
curl http://localhost:3000/health
```

Expected response:
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "service": "parking-management-api",
  "version": "1.0.0"
}
```

### 2. Check Frontend
- Navigate to http://localhost:4200
- You should see the login page
- Try logging in with the default credentials

### 3. Check Database Connection
```bash
mysql -u parking_user -p parking_management -e "SHOW TABLES;"
```

Expected tables:
- users
- plots
- parking_slots
- vehicles
- guests
- parking_logs

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### 1. Port Already in Use
**Error**: `EADDRINUSE: address already in use :::3000`

**Solution**:
```bash
# Find process using the port
lsof -ti:3000
# Kill the process
kill -9 <PID>

# Or use different ports
# Backend: Change PORT in .env
# Frontend: ng serve --port 4201
```

#### 2. Database Connection Failed
**Error**: `ER_ACCESS_DENIED_ERROR`

**Solutions**:
- Verify MySQL is running: `systemctl status mysql`
- Check credentials in `.env` file
- Ensure database exists: `SHOW DATABASES;`
- Verify user permissions: `SHOW GRANTS FOR 'parking_user'@'localhost';`

#### 3. Module Not Found Errors
**Error**: `Cannot find module '@angular/core'`

**Solution**:
```bash
# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### 4. CORS Issues
**Error**: `Access to XMLHttpRequest blocked by CORS policy`

**Solution**:
- Verify `FRONTEND_URL` in backend `.env`
- Check `apiUrl` in frontend `environment.ts`
- Ensure both servers are running

#### 5. Angular CLI Not Found
**Error**: `ng: command not found`

**Solution**:
```bash
npm install -g @angular/cli
```

### 6. MySQL Authentication Issues
**Error**: `ER_NOT_SUPPORTED_AUTH_MODE`

**Solution**:
```sql
ALTER USER 'parking_user'@'localhost' IDENTIFIED WITH mysql_native_password BY 'parking_password';
FLUSH PRIVILEGES;
```

## 🔧 Development Commands

### Backend Commands
```bash
cd backend

# Development
npm run start:dev      # Start with hot reload
npm run start:debug    # Start with debugging
npm run start:prod     # Start production build

# Testing
npm run test           # Unit tests
npm run test:e2e       # End-to-end tests
npm run test:cov       # Test coverage

# Building
npm run build          # Build for production
```

### Frontend Commands
```bash
cd frontend

# Development
ng serve               # Start dev server
ng serve --port 4201   # Start on different port
ng serve --open        # Start and open browser

# Testing
ng test                # Unit tests
ng e2e                 # End-to-end tests

# Building
ng build               # Build for production
ng build --prod        # Production build with optimizations
```

## 🌐 Environment Configuration

### Backend Environment Variables (.env)
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=parking_user
DB_PASSWORD=parking_password
DB_DATABASE=parking_management

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=15m
JWT_REFRESH_SECRET=your-super-secret-refresh-key
JWT_REFRESH_EXPIRES_IN=7d

# Application Configuration
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:4200

# CORS Configuration
CORS_ORIGIN=http://localhost:4200
CORS_CREDENTIALS=true
```

### Frontend Environment (src/environments/environment.ts)
```typescript
export const environment = {
  production: false,
  apiUrl: 'http://localhost:3000'
};
```

## 📞 Support

If you encounter any issues during installation:

1. **Check the logs** in the terminal for error messages
2. **Verify all prerequisites** are installed correctly
3. **Review the troubleshooting section** above
4. **Create an issue** on GitHub with:
   - Your operating system
   - Node.js version (`node --version`)
   - npm version (`npm --version`)
   - Error messages and logs

## 🎉 Next Steps

After successful installation:

1. **Explore the Admin Dashboard** - Log in as admin to see system overview
2. **Create Your First Plot** - Log in as a user and create a parking plot
3. **Add Vehicles** - Register vehicles for parking assignments
4. **Manage Guests** - Set up expected visitors
5. **Review the API** - Check out the API documentation at http://localhost:3000/api

Happy parking management! 🚗✨
